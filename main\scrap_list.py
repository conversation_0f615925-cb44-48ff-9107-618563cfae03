import requests
from bs4 import BeautifulSoup, NavigableString
import pandas as pd
import time
import random
# import urllib.request
import os
import re

"""
增加字段：
1. 用户 id 字段
2. 途径/形成，景点字段，使用"|"字符分割存入一个单元格


代理池
1. 使用私密代理，爬取数据，给出各种类型的代理代码整理方式：https://juejin.cn/post/7035782853237407758。基于 demo4 来写

2. 免费代理池 https://github.com/tongsq/proxy-collect?tab=readme-ov-file，未测试。
3. 免费代理池 https://github.com/jhao104/proxy_pool?tab=readme-ov-file，代理池少且无效
"""


# 添加多个User-Agent列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/2.5.0.298",
    "Safari/macOS: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.2 Safari/605.1.15",
    "Opera/Windows: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 OPR/73.0.3856.329",
    "Vivaldi/Windows: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Vivaldi/3.5",
    "Internet-Explorer/Windows: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.215 Safari/537.36 TBC/1.3.3.999 Thunder/12.1.6.2780"
]

"""
需求：
输入：多种代理 ip 池格式，并提供验证代理 ip 池是否可用
输出：可用的代理 ip

1. list，格式：['http://proxy:port', 'http://proxy:port']
2. dict，格式：{'http': 'http://proxy:port', 'https': 'http://proxy:port'}
3. 从redis中读取，127.0.0.1:5010/get
4. 从txt文件中读取，一行一个ip（http://proxy:port /n https://proxy:port）

统一格式为：
dict [{'http':'http://proxy:port'}, {'https':'http://proxy:port'}]


#TODO 感觉出发日期和游记的创建日期都需要记录下来

"""


# 私密代理池
proxylist = [
    {'https':'*************:17841'},
    {'https':'*************:6379'},
    {'https':'*************:22753'},
    {'http':'************:80'},
    {'http':'************:13001'},
    {'http':'************:80'}
]

# def test_proxy(proxy):
#     '''
#     测试代理是否可用
#     '''
#     try:
#         print(f'测试代理: {proxy}')
#         httpProxyHandler = urllib.request.ProxyHandler(proxy)
#         opener = urllib.request.build_opener(httpProxyHandler)
#         request = urllib.request.Request('https://travel.qunar.com/')
#         response = opener.open(request, timeout=5)
#         print('代理可用!')
#         return True
#     except Exception as e:
#         print(f'代理连接错误: {e}')
#         return False
    
def test_proxy(proxy, target_url):
    """
    测试代理是否可用
    输入 proxy: 字典格式，如 {"http": "http://proxy:port", "https": "http://proxy:port"}  
    输入 target_url: 目标 url

    输出: True: 代理可用; False: 代理不可用
    """
    try:
        response = requests.get(target_url, proxies=proxy, timeout=10)
        if response.status_code == 200:
            return True
        else:
            return False
    except Exception as e:
        print(f'代理连接错误: {e}')
        return False


def get_working_proxy():
    '''
    从代理池中获取一个可用的代理
    '''
    # 随机打乱代理列表顺序
    random_proxies = random.sample(proxylist, len(proxylist))
    target_url = 'https://travel.qunar.com/'
    for proxy in random_proxies:
        if test_proxy(proxy, target_url):
            return proxy
        
    print("所有代理均不可用，将直接连接")
    return None


def fetchHotel(url):

    # 添加随机延时，模拟人类浏览行为
    sleep_time = random.uniform(5, 10)  # 增加延时范围

    print(f"等待 {sleep_time:.2f} 秒...")
    time.sleep(sleep_time)
    
    # 随机选择一个User-Agent
    user_agent = random.choice(USER_AGENTS)
    print(f"使用User-Agent: {user_agent}")

    # 发起网络请求，获取数据
    headers = {
        # 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        # 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36',
        "user-agent": user_agent,
    }

    # 发起网络请求
    r = requests.get(url, headers=headers, timeout=30)  # 添加超时设置
    r.encoding = "utf-8"
    return r.text

    # # 获取可用代理
    # proxy = get_working_proxy()
    
    # # 最多重试3次
    # for attempt in range(3):
    #     try:
    #         if proxy:
    #             print(f"使用代理 {proxy} 发起请求")
    #             r = requests.get(url, headers=headers, proxies=proxy, timeout=15)
    #         else:
    #             print("直接连接发起请求")
    #             r = requests.get(url, headers=headers, timeout=15)
                
    #         r.raise_for_status()  # 如果请求不成功则抛出异常
    #         r.encoding = "utf-8"
    #         return r.text
            
    #     except Exception as e:
    #         print(f"请求失败 (尝试 {attempt+1}/3): {e}")
    #         if proxy:
    #             print("当前代理不可用，尝试获取新代理")
    #             proxy = get_working_proxy()
    #         if attempt == 2:  # 最后一次尝试，直接连接
    #             print("尝试不使用代理直接连接")
    #             try:
    #                 r = requests.get(url, headers=headers, timeout=15)
    #                 r.raise_for_status()
    #                 r.encoding = "utf-8"
    #                 return r.text
    #             except Exception as e2:
    #                 print(f"直接连接也失败: {e2}")
    #                 return ""  # 返回空字符串，表示获取失败
                    
    # return ""  # 如果所有尝试都失败，返回空字符串


def getPageNum(html):
    # 获取总页数
    pageNum = 1
    # 使用 BeautifulSoup 解析 HTML
    bsObj = BeautifulSoup(html, "html.parser")
    pageList = bsObj.find("div", attrs={"class": "b_paging"}).find_all("a")  # 找出第一个 "class": "b_paging" 的 <div> 中的所有 <a> 标签。以 list 形式返回所有 <a> 标签
    if pageList:
        pageNum = pageList[-2].text  # 倒数第二个 <a> 标签是总页码
    
    # pageNum = 5  # 限定 pageNum 数量
    
    return int(pageNum)

def getPalce(book):
    # 提取所有 <p class="places"> 标签
    places_tags = book.find_all('p', class_='places')

    # 定义匹配汉字和冒号的正则表达式
    # \u4e00-\u9fff 是汉字的 Unicode 范围
    # 我们同时匹配半角 : 和全角 ：
    char_pattern = re.compile(r'[\u4e00-\u9fff:：]') # 保留冒号以备后续去除

    filtered_texts = []

    for tag in places_tags:
        result_chars = []
        for element in tag.descendants: # 遍历所有后代节点
            if isinstance(element, NavigableString):
                # 如果是文本节点，提取所有匹配的字符
                found_chars = char_pattern.findall(str(element))
                result_chars.extend(found_chars)
            elif element.name == 'b' and element.string == '>>':
                # 如果是 <b>>></b> 标签，添加 '>'
                result_chars.append('>')
            # 其他标签（如 <span>）的文本会在其内部的 NavigableString 被处理

        # 将提取到的字符列表连接成字符串
        filtered_texts.append("".join(result_chars))
        # print(filtered_texts)

    final_string = ""
    if len(filtered_texts) >= 2:
        # 处理第一个字符串：去除 "途经" 和冒号
        part1 = filtered_texts[0].replace("途经", "").replace(":", "").replace("：", "")
        # 处理第二个字符串：去除 "行程" 和冒号
        part2 = filtered_texts[1].replace("行程", "").replace(":", "").replace("：", "")
        # 拼接结果
        final_string = f"{part1}>{part2}"
    elif len(filtered_texts) == 1:
        part1 = ''
        if "途经" in filtered_texts[0]: 
            # 如果只有一个结果，则只处理第一个
            part1 = filtered_texts[0].replace("途经", "").replace(":", "").replace("：", "")
        elif "行程" in filtered_texts[0]:
            part1 = filtered_texts[0].replace("行程", "").replace(":", "").replace("：", "")
        final_string = part1
    else:
        return ""
    return final_string


def parseHtml(html):
    # 解析 html 网页，提取数据
    bsObj = BeautifulSoup(html, "html.parser")
    # 攻略列表存放在 <ul class='b_strategy_list'> 的 <li> 中
    # 返回第一个 "class": "b_strategy_list" 的 <ul> 标签
    bookList = bsObj.find("ul", attrs={"class": "b_strategy_list"})  
    
    # print(bookList)

    # 如果没有找到符合条件的 <ul>，直接返回空列表
    if not bookList:
        return []
    
    books = []

    for book in bookList:  # 遍历 <ul> 中的每一个 <li> 标签，用 book 表示每个 <li> 标签

        link = "https://travel.qunar.com" + book.h2.a["href"]  # 获取游记链接

        if 'youji' in link:
            link = link.replace("youji", "travelbook/note")
        
        title = book.h2.a.text  # 获取游记标题

        travel_id = link.split("/")[-1]  # 获取游记id

        user_info = book.find("p", attrs={"class": "user_info"})  

        intro = user_info.find("span", attrs={"class": "intro"})
        user_name = intro.find("span", attrs={"class": "user_name"}).text  # 获取用户名

        if intro.find("span", attrs={"class": "visit"}) is not None:  # 如果用户为 visit 那么忽略这条记录，因为无法获取游记正文。
            continue

        user_link_ = intro.find("span", attrs={"class": "user_name"}).a["href"]  # 用户主页链接，str
        user_link = "https:" + user_link_  # 添加 https。感觉可选，如果如果直接将 user_link_ 作为链接，则不需要添加 https

        user_link_parts = user_link.split("/space/")  # 将链接分割成两部分
        user_note_link = user_link_parts[0] + "/space/notes/" + user_link_parts[1]  # 构建新的链接--用户游记列表页面链接

        # 提取 / 和 @qunar 之间的部分
        user_link_after_char = user_link.split("/")[-1]  # 从最后一个 "/" 后的部分开始
        user_id = user_link_after_char.split("@")[0]      # 提取 "@" 之前的部分


        date = intro.find("span", attrs={"class": "date"}).text  # 获取出发日期
        days = intro.find("span", attrs={"class": "days"}).text  # 获取旅行天数

        places = getPalce(book)  # 景点列表，用">"分隔

        photoTmp = intro.find("span", attrs={"class": "photo_nums"})  # 获取照片数
        if photoTmp:
            photo_nums = photoTmp.text
        else:
            photo_nums = "没有照片"

        peopleTmp = intro.find("span", attrs={"class": "people"})  # 获取人数
        if peopleTmp:
            people = peopleTmp.text
        else:
            people = ""

        tripTmp = intro.find("span", attrs={"class": "trip"})  # 获取玩法，如短途周末
        if tripTmp:
            trip = tripTmp.text
        else:
            trip = ""

        feeTmp = intro.find("span", attrs={"class": "fee"})  # 获取费用
        if feeTmp:
            fee = feeTmp.text
        else:
            fee = ""

        nums = user_info.find("span", attrs={"class": "nums"})  
        icon_view = nums.find("span", attrs={"class": "icon_view"}).span.text  # 获取阅读数
        icon_love = nums.find("span", attrs={"class": "icon_love"}).span.text  # 获取点赞数
        icon_comment = nums.find("span", attrs={"class": "icon_comment"}).span.text  # 获取评论数

        book_data = [
            title,
            travel_id,
            link,
            user_name,
            user_id,
            user_note_link,
            date,
            days,
            places,
            photo_nums,
            people,
            trip,
            fee,
            icon_view,
            icon_love,
            icon_comment,
        ]
        books.append(book_data)  # 将每篇游记数据添加到books列表中
    
    return books  # 返回所有游记数据的列表，而不是使用yield


def saveCsvFile(filename, content, write_header=False):
    # 保存文件
    dataframe = pd.DataFrame(content)
    
    # 检查文件是否存在
    file_exists = os.path.isfile(filename)
    
    # 如果文件已存在且不需要写入标题，或者文件不存在且需要写入标题
    if (file_exists and not write_header) or (not file_exists and write_header):
        dataframe.to_csv(
            filename, 
            encoding="utf_8_sig", 
            mode="a", 
            index=False, 
            sep=",", 
            header=write_header
        )
    else:
        dataframe.to_csv(
            filename, 
            encoding="utf_8_sig", 
            mode="w", 
            index=False, 
            sep=",", 
            header=write_header
        )


def downloadBookInfo(url, fileName):
    # 定义标题行
    columns = [
        "标题",
        "游记id",
        "游记链接",
        "作者名",
        "作者id",
        "作者游记页链接",
        "出发日期",  # 计划删除
        "天数",  # 计划删除 
        "景点",
        "照片数",
        "人数",  # 计划删除
        "玩法",  # 计划删除
        "费用",  # 计划删除
        "阅读数",  # 计划删除
        "点赞数",
        "评论数",  # 计划删除
    ]
    
    # 检查文件是否存在
    file_exists = os.path.isfile(fileName)
    
    # 如果文件不存在，则创建并写入标题行
    if not file_exists:
        # 创建一个空的DataFrame并写入标题行
        df = pd.DataFrame(columns=columns)
        df.to_csv(fileName, encoding="utf_8_sig", index=False)
        print(f"创建新文件 {fileName} 并写入标题行")

    html = fetchHotel(url)  # 返回指定 url 页面的 html 语句
    
    if not html:
        print("获取首页失败，程序退出")
        return
    
    pageNum = getPageNum(html)
    
    # 解析第一页内容
    books = parseHtml(html)
    if books:
        # 将第一页数据写入CSV
        dataframe = pd.DataFrame(books, columns=columns)
        dataframe.to_csv(fileName, encoding="utf_8_sig", mode="a", index=False, header=False)
        print(f"第1页数据已保存，共{len(books)}条记录")

    # 处理剩余页面
    for page in range(2, pageNum + 1):
        print("正在爬取", str(page), "页 ......")
        # 每页之间添加更长的随机延时
        page_sleep = random.uniform(8, 12)  # 8-12秒
        print(f"页面间等待 {page_sleep:.2f} 秒...")
        time.sleep(page_sleep)

        url = (
            "https://travel.qunar.com/travelbook/list/" + city + "/hot_heat/"
            + str(page)
            + ".htm"
        )
        
        html = fetchHotel(url)

        if not html:
            print(f"获取第{page}页失败，跳过该页")
            continue

        # 解析当前页面内容
        books = parseHtml(html)
        if books:
            # 将当前页数据写入CSV
            dataframe = pd.DataFrame(books, columns=columns)
            dataframe.to_csv(fileName, encoding="utf_8_sig", mode="a", index=False, header=False)
            print(f"第{page}页数据已保存，共{len(books)}条记录")

city = "上海"
url = "https://travel.qunar.com/travelbook/list/" + city + "/hot_heat/1.htm"

fileName = "data_" + city + ".csv"

downloadBookInfo(url, fileName)
print("全部完成！")