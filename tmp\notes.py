import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import os

"""
不爬这个数据。如果要爬游记列表来获取指定用户的历史游记列表，那同样的对于景点相关列表也要去爬。


爬取用户游记列表，获取用户的历史游记信息

ul-li中存储历史游记信息，但是一次只展示 10 个。需要模拟滚动，参考：https://blog.csdn.net/simon1223z/article/details/125540760
"""

# 添加多个User-Agent列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1"
]


def fetchHotel(url):
    # 添加随机延时，模拟人类浏览行为
    sleep_time = random.uniform(5, 10)  # 增加延时范围

    print(f"等待 {sleep_time:.2f} 秒...")
    time.sleep(sleep_time)
    
    # 随机选择一个User-Agent
    user_agent = random.choice(USER_AGENTS)
    # print(f"使用User-Agent: {user_agent}")

    # 发起网络请求，获取数据
    headers = {
        # 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        # 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36',
        "user-agent": user_agent,
    }

    # 发起网络请求
    r = requests.get(url, headers=headers)
    r.encoding = "utf-8"
    return r.text



def getPageNum(html):
    # 获取总页数
    pageNum = 1
    # 使用 BeautifulSoup 解析 HTML
    bsObj = BeautifulSoup(html, "html.parser")
    pageList = bsObj.find("div", attrs={"class": "b_paging"}).find_all("a")  # 找出第一个 "class": "b_paging" 的 <div> 中的所有 <a> 标签。以 list 形式返回所有 <a> 标签
    if pageList:
        pageNum = pageList[-2].text  # 倒数第二个 <a> 标签是总页码
    
    pageNum = 5  # 限定 pageNum 数量
    
    return int(pageNum)


def parseHtml(html):
    # 解析 html 网页，提取数据
    bsObj = BeautifulSoup(html, "html.parser")
    # 攻略列表存放在 <ul class='b_strategy_list'> 的 <li> 中
    # 返回第一个 "class": "b_strategy_list" 的 <ul> 标签
    bookList = bsObj.find("ul", attrs={"class": "b_strategy_list"})  
    
    # print(bookList)

    # 如果没有找到符合条件的 <ul>，直接返回空列表
    if not bookList:
        return []
    
    books = []

    for book in bookList:  # 遍历 <ul> 中的每一个 <li> 标签，用 book 表示每个 <li> 标签

        link = "https://travel.qunar.com" + book.h2.a["href"]  # 获取游记链接
        title = book.h2.a.text  # 获取游记标题
        user_info = book.find("p", attrs={"class": "user_info"})  

        intro = user_info.find("span", attrs={"class": "intro"})
        user_name = intro.find("span", attrs={"class": "user_name"}).text  # 获取用户名

        user_link_ = intro.find("span", attrs={"class": "user_name"}).a["href"]  # 用户主页链接，str
        user_link = "https:" + user_link_  # 添加 https。感觉可选，如果如果直接将 user_link_ 作为链接，则不需要添加 https

        user_link_parts = user_link.split("/space/")  # 将链接分割成两部分
        user_link = user_link_parts[0] + "/space/notes/" + user_link_parts[1]  # 构建新的链接--用户游记列表页面链接


        date = intro.find("span", attrs={"class": "date"}).text  # 获取出发日期
        days = intro.find("span", attrs={"class": "days"}).text  # 获取旅行天数

        photoTmp = intro.find("span", attrs={"class": "photo_nums"})  # 获取照片数
        if photoTmp:
            photo_nums = photoTmp.text
        else:
            photo_nums = "没有照片"

        peopleTmp = intro.find("span", attrs={"class": "people"})  # 获取人数
        if peopleTmp:
            people = peopleTmp.text
        else:
            people = ""

        tripTmp = intro.find("span", attrs={"class": "trip"})  # 获取玩法，如短途周末
        if tripTmp:
            trip = tripTmp.text
        else:
            trip = ""

        feeTmp = intro.find("span", attrs={"class": "fee"})  # 获取费用
        if feeTmp:
            fee = feeTmp.text
        else:
            fee = ""

        nums = user_info.find("span", attrs={"class": "nums"})  
        icon_view = nums.find("span", attrs={"class": "icon_view"}).span.text  # 获取阅读数
        icon_love = nums.find("span", attrs={"class": "icon_love"}).span.text  # 获取点赞数
        icon_comment = nums.find("span", attrs={"class": "icon_comment"}).span.text  # 获取评论数

        book_data = [
            title,
            link,
            user_name,
            user_link,
            date,
            days,
            photo_nums,
            people,
            trip,
            fee,
            icon_view,
            icon_love,
            icon_comment,
        ]
        books.append(book_data)  # 将每篇游记数据添加到books列表中
    
    return books  # 返回所有游记数据的列表，而不是使用yield


def saveCsvFile(filename, content, write_header=False):
    # 保存文件
    dataframe = pd.DataFrame(content)
    
    # 检查文件是否存在
    file_exists = os.path.isfile(filename)
    
    # 如果文件已存在且不需要写入标题，或者文件不存在且需要写入标题
    if (file_exists and not write_header) or (not file_exists and write_header):
        dataframe.to_csv(
            filename, 
            encoding="utf_8_sig", 
            mode="a", 
            index=False, 
            sep=",", 
            header=write_header
        )
    else:
        dataframe.to_csv(
            filename, 
            encoding="utf_8_sig", 
            mode="w", 
            index=False, 
            sep=",", 
            header=write_header
        )


def downloadBookInfo(url, fileName):
    # 定义标题行
    columns = [
        "标题",
        "链接",
        "作者",
        "作者游记列表链接",
        "出发日期",
        "天数",
        "照片数",
        "人数",
        "玩法",
        "费用",
        "阅读数",
        "点赞数",
        "评论数",
    ]
    
    # 检查文件是否存在
    file_exists = os.path.isfile(fileName)
    
    # 如果文件不存在，则创建并写入标题行
    if not file_exists:
        # 创建一个空的DataFrame并写入标题行
        df = pd.DataFrame(columns=columns)
        df.to_csv(fileName, encoding="utf_8_sig", index=False)
        print(f"创建新文件 {fileName} 并写入标题行")

    html = fetchHotel(url)  # 返回指定 url 页面的 html 语句
    
    if not html:
        print("获取首页失败，程序退出")
        return
    
    pageNum = getPageNum(html)
    
    # 解析第一页内容
    books = parseHtml(html)
    if books:
        # 将第一页数据写入CSV
        dataframe = pd.DataFrame(books, columns=columns)
        dataframe.to_csv(fileName, encoding="utf_8_sig", mode="a", index=False, header=False)
        print(f"第1页数据已保存，共{len(books)}条记录")

    # 处理剩余页面
    for page in range(2, pageNum + 1):
        print("正在爬取", str(page), "页 ......")
        # 每页之间添加更长的随机延时
        page_sleep = random.uniform(8, 12)  # 8-12秒
        print(f"页面间等待 {page_sleep:.2f} 秒...")
        time.sleep(page_sleep)

        url = (
            "https://travel.qunar.com/travelbook/list/%E4%B8%8A%E6%B5%B7/hot_heat/"
            + str(page)
            + ".htm"
        )
        
        html = fetchHotel(url)

        if not html:
            print(f"获取第{page}页失败，跳过该页")
            continue

        # 解析当前页面内容
        books = parseHtml(html)
        if books:
            # 将当前页数据写入CSV
            dataframe = pd.DataFrame(books, columns=columns)
            dataframe.to_csv(fileName, encoding="utf_8_sig", mode="a", index=False, header=False)
            print(f"第{page}页数据已保存，共{len(books)}条记录")


url = "https://travel.qunar.com/travelbook/list/上海/hot_heat/1.htm"
fileName = "data_for_test.csv"

downloadBookInfo(url, fileName)
print("全部完成！")