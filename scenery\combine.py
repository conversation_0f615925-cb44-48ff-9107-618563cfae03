"""
合并两个csv文件，按照 travelId 去重
"""

import pandas as pd

def combine_csv(file1, file2):
    df1 = pd.read_csv(file1, encoding="utf-8")
    df2 = pd.read_csv(file2, encoding="utf-8")

    combined_df = pd.concat([df1, df2], ignore_index=True)

    unique_df = combined_df.drop_duplicates(subset=['travelId'], keep='first')
    print(unique_df.head(2))
    unique_df.to_csv("scenery/result.csv", index=False, encoding="utf-8")

    return unique_df


combine_csv("D:\Code\PythonCode\scrape\scenery\data_filtered_fenci_scenery_1500.csv",
            "D:\Code\PythonCode\scrape\scenery\data_filtered_fenci_scenery_3500.csv")
