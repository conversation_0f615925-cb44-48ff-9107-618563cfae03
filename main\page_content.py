import os
import html2text as ht
from bs4 import BeautifulSoup
import requests
import pandas as pd
import time
from random import randint
import random
import logging
import datetime
import urllib.parse
import argparse
# from scrap_list import city

"""
注意需要安装 xlmx库，用于解析页面内容

#TODO 将文件路径和文件名统一放到 main() 中

"""

# 配置日志
def setup_logger():
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    log_filename = f"{log_dir}/scraper_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger()

logger = setup_logger()

# 添加多个User-Agent列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/2.5.0.298",
    "Safari/macOS: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.2 Safari/605.1.15",
    "Opera/Windows: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 OPR/73.0.3856.329",
    "Vivaldi/Windows: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Vivaldi/3.5",
    "Internet-Explorer/Windows: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.215 Safari/537.36 TBC/1.3.3.999 Thunder/12.1.6.2780"
] 

def download_image(img_url, travel_id, img_index, max_retries=3):
    """
    下载图片并保存到指定文件夹
    
    参数:
    img_url - 图片URL
    travel_id - 游记ID，用于创建文件夹
    img_index - 图片序号，用于命名
    
    返回:
    保存的文件路径或None（失败时）
    """
    for attempt in range(max_retries):
        try:
            # 创建保存图片的文件夹
            img_dir = f"img/{travel_id}"
            os.makedirs(img_dir, exist_ok=True)
            
            # 获取文件扩展名
            file_ext = os.path.splitext(urllib.parse.urlparse(img_url).path)[1]
            if not file_ext:
                file_ext = ".jpg"  # 默认扩展名
                
            # 构建文件名
            filename = f"{img_dir}/{img_index}{file_ext}"
            
            # 设置请求头
            user_agent = random.choice(USER_AGENTS)
            headers = {
                "User-Agent": user_agent,
                "Referer": "https://travel.qunar.com/"
            }
            
            # 下载图片
            response = requests.get(img_url, headers=headers, stream=True, timeout=40)
            response.raise_for_status()
            
            # 保存图片
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info(f"图片已保存: {filename}")
            
            # 等待5-10秒
            wait_time = random.uniform(5, 7)
            logger.info(f"等待 {wait_time:.2f} 秒...")
            time.sleep(wait_time)
            return filename
        except requests.exceptions.RequestException as e:
            if attempt < max_retries + 1:
                logger.error(f"下载图片失败: {img_url}, 正在重试({attempt + 1}/{max_retries}): {img_url}")
                time.sleep(2)
                continue
            logger.error(f"下载图片失败: {img_url}, 错误: {e}")
        except Exception as e:
            logger.error(f"下载图片失败: {img_url}, 错误: {e}")
        return None
    

def fetchUrl(url):
    '''
    发起网络请求
    '''
    
    # 添加随机延时，模拟人类浏览行为
    sleep_time = random.uniform(8, 10)  # 增加延时范围

    logger.info(f"等待 {sleep_time:.2f} 秒...")
    time.sleep(sleep_time)

    # 随机选择一个User-Agent
    user_agent = random.choice(USER_AGENTS)
    logger.info(f"使用User-Agent: {user_agent}")

    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "user-agent": user_agent,
    }
    try:
        r = requests.get(url, headers=headers, timeout=30)  # 添加超时设置
        r.raise_for_status()
        r.encoding = "utf-8"
        return r.text
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP错误: {e}, URL: {url}, 状态码: {e.response.status_code}")
    except requests.exceptions.ConnectionError as e:
        logger.error(f"连接错误: {e}, URL: {url}")
    except requests.exceptions.Timeout as e:
        logger.error(f"请求超时: {e}, URL: {url}")
    except requests.exceptions.RequestException as e:
        logger.error(f"请求异常: {e}, URL: {url}")
    return None


def getContent(html, title="", travel_id=""):
    '''
    提取文章的正文部分的 html
    '''
    if not html:
        logger.error(f"HTML内容为空，无法解析: {title}")
        return ""
    
    try:
        html = html.replace("&nbsp;", "")
        html = html.replace("~~", "~").replace("~~", "~")

        bsObj = BeautifulSoup(html, 'lxml')
        
        # 尝试多种可能的内容区域选择器
        content = bsObj.find("div", attrs={"id":"b_panel_schedule"})
        
        if not content:
            logger.error(f"未找到内容区域(b_panel_schedule或其他): {title}")
            return ""

        # 处理图片，先检查是否存在img元素
        imgs = content.find_all("img")
        if imgs:
            logger.info(f"找到 {len(imgs)} 张图片")
            img_count = 0

            for i, img in enumerate(imgs, 1):
                try:
                    # 确保必要属性存在
                    if 'data-original' in img.attrs:
                        src = img['data-original']
                        if travel_id:
                            img_path = download_image(src, travel_id, i)
                            if img_path:
                                img_count += 1
                        if 'title' in img.attrs:
                            txt = img['title']
                        else:
                            txt = ""
                        img.insert_after("![{0}]({1})".format(txt, src))
                        img.extract()  # bs4中的方法，将 html 中 img 的标签从文档中删除
                except Exception as e:
                    logger.warning(f"处理图片时出错: {e}, 文章: {title}")
                    continue
            if img_count < len(imgs):
                logger.error(f"成功处理 {img_count} 张图片，还有 {len(imgs) - img_count} 张图片未处理！")
        

        # 处理景点信息 <a data-poi-id="xxx">
        # poi = content.find_all("")

        # TODO 有点击展开全部，游记"上海四晚五日亲子游" md 文件中，是否有文本 "在阳光的照射下呈现出斑斓柔和的色彩"
        # TODO 发布一篇帖子看看！


        # 处理h5标题，先检查是否存在h5元素
        header5 = content.find_all("h5")
        if header5:
            for h5 in header5:
                try:
                    t5 = h5.find("div", attrs={"class":"b_poi_title_box"})
                    if t5:
                        h5.insert_after("##### " + t5.text)
                        h5.extract()
                except Exception as e:
                    logger.warning(f"处理h5标题时出错: {e}, 文章: {title}")
                    continue

        # # 处理评论，先检查是否存在ops元素
        # cmts = content.find_all("div", attrs={"class":"ops"})
        # if cmts:
        #     for s in cmts:
        #         try:
        #             s.insert_after('< br/>')
        #             s.extract()
        #         except Exception as e:
        #             logger.warning(f"处理评论时出错: {e}, 文章: {title}")
        #             continue

        return str(content)
    except Exception as e:
        logger.error(f"解析HTML内容时出错: {e}, 文章: {title}")
        return ""

# TODO 将文章正文部分转成 str 保存到csv文件中


def Html2Markdown(html, title=""):
    '''
    将文章正文部分由 html 格式转换成 Markdown 格式
    '''
    if not html:
        logger.error(f"转换为Markdown失败：HTML内容为空, 文章: {title}")
        return ""
        
    try:
        text_maker = ht.HTML2Text()
        text = text_maker.handle(html)
        text = text.replace("#\n\n", "# ")
        text = text.replace("\.",".")
        text = text.replace(".\n",". ")
        text = text.replace("< br/>","\n")
        text = text.replace("tr-\n","tr-")
        text = text.replace("查看全部 __","")
        return text
    except Exception as e:
        logger.error(f"HTML转Markdown时出错: {e}, 文章: {title}")
        return ""

def saveMarkdownFile(title, travel_id, content):
    '''
    保存文本到 Markdown 文件中
    title：标题（仅用于日志）
    travel_id：游记ID，作为文件名
    content：要保存的文本内容
    '''
    if not content:
        logger.error(f"保存失败: {title}, 内容为空")
        return 
    try:
        os.makedirs("data", exist_ok=True)  # 确保输出目录存在，若存在则不处理
        city = '上海'
        travel_id_str = str(travel_id)  # 确保为 str
        filepath = os.path.join("data", city, f"{travel_id_str}.md")
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"成功保存文件: {travel_id_str}.md")
    except Exception as e:
        logger.error(f"保存文件时出错: {title}, 文件名: {travel_id}.md, 错误: {e}")


def main(start_row=0):
    logger.info("开始爬取...")
    try:
        df = pd.read_csv('data.csv', sep=',')
        success_count = 0
        failed_count = 0

        # 从指定行开始爬取
        if start_row > 0:
            df = df[start_row:]
            logger.info(f"从第 {start_row} 行开始爬取，共 {len(df)} 条记录")
        
        # 创建一个记录失败项的列表
        failed_items = []
        
        for index, row in df.iterrows():
            try:
                title = row.iloc[0]  # 第一列是标题
                travel_id = row.iloc[1]  # 第二列是travel_id
                url = row.iloc[2]  # 第三列是URL
                
                logger.info(f"\n正在处理 [{index+1}/{len(df)}]: {title} (ID: {travel_id})")
                
                # 检查并修正URL
                original_url = url
                if 'youji' in url:
                    url = url.replace("youji", "travelbook/note")

                # 获取HTML内容
                html = fetchUrl(url)
                if not html:
                    error_msg = f"URL获取失败"
                    logger.error(f"跳过处理: {title}, {error_msg}")
                    failed_count += 1
                    failed_items.append({"title": title, "travel_id": travel_id, "url": url, "reason": error_msg})
                    continue

                if "非常抱歉，您访问的页面不存在" in html:
                    error_msg = f"页面不存在，可能被删除或 url 错误"
                    logger.error(f"跳过处理: {title}, {error_msg}")
                    failed_count += 1
                    failed_items.append({"title": title, "travel_id": travel_id, "url": url, "reason": error_msg})
                    continue
                
                # 提取内容
                content = getContent(html, title, travel_id)
                if not content:
                    error_msg = f"内容提取失败"
                    logger.error(f"跳过处理: {title}, {error_msg}")
                    failed_count += 1
                    failed_items.append({"title": title, "travel_id": travel_id, "url": url, "reason": error_msg})
                    continue
                
                # 转换为Markdown
                md = Html2Markdown(content, title)
                if not md:
                    error_msg = f"Markdown转换失败"
                    logger.error(f"跳过处理: {title}, {error_msg}")
                    failed_count += 1
                    failed_items.append({"title": title, "travel_id": travel_id, "url": url, "reason": error_msg})
                    continue
                
                # 保存文件
                saveMarkdownFile(title, travel_id, md)
                success_count += 1

                # 随机等待时间，避免爬取过于频繁触发反爬机制
                t = randint(5, 10)
                logger.info(f"等待 {t} 秒后继续...")
                time.sleep(t)
                
            except Exception as e:
                error_msg = f"处理文章时出错: {str(e)}"
                logger.error(f"{error_msg}, 文章: {title if 'title' in locals() else '未知'}")
                failed_count += 1
                failed_items.append({
                    "title": title if 'title' in locals() else "未知", 
                    "travel_id": travel_id if 'travel_id' in locals() else "未知", 
                    "url": url if 'url' in locals() else "未知", 
                    "reason": error_msg
                })
                continue
                
        logger.info(f"\n爬取完成! 成功: {success_count}, 失败: {failed_count}")
        
        # 将失败项保存到CSV文件中
        if failed_items:
            failed_df = pd.DataFrame(failed_items)
            failed_file = f"failed_items_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            failed_df.to_csv(failed_file, index=False, encoding='utf-8')
            logger.info(f"已将失败项保存至: {failed_file}")
            
    except Exception as e:
        logger.error(f"程序执行出错: {e}")


# 启动爬虫
if __name__ == "__main__":
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='从CSV文件爬取旅游数据')
    parser.add_argument('--start', type=int, default=0,
                        help='从CSV的第几行开始爬取(0表示从头开始)')
    args = parser.parse_args()
    
    # 如果没有提供命令行参数，则交互式询问用户
    start_row = args.start
    if start_row == 0:
        try:
            user_input = input("请输入从CSV的第几行开始爬取(直接回车默认从头开始): ")
            if user_input.strip():
                start_row = int(user_input)
        except ValueError:
            print("输入无效，将从头开始爬取")
            start_row = 0
    
    # 调用主函数，传入起始行
    main(start_row)
    print("爬取完成！")
    logger.info("爬取完成！")