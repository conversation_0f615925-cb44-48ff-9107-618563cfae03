{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["上海四晚五日亲子游\n", "https://travel.qunar.com/youji/6815261\n", "上海迪士尼2日游图文攻略不买尊享卡狂刷12主项+3大节目+22个必看小提示\n", "https://travel.qunar.com/youji/7672840\n", "周末，用美食奖赏自己—那些我爱的上海馆子\n", "https://travel.qunar.com/youji/6591970\n", "遇见迪士尼 —— 炎炎夏日里的三日狂欢\n", "https://travel.qunar.com/youji/6906928\n", "发现徐家汇，钟情小马路\n", "https://travel.qunar.com/youji/7000995\n", "魔都历史街区&名人故居1日游\n", "https://travel.qunar.com/youji/7004876\n", "定格最美的上海\n", "https://travel.qunar.com/youji/6352590\n", "小小旅行家的“精灵大师梦”\n", "https://travel.qunar.com/youji/7525750\n", "共青国家森林公园：用美景纪念青春\n", "https://travel.qunar.com/youji/7024409\n", "情深深雨蒙蒙--漫步外滩\n", "https://travel.qunar.com/youji/6973257\n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv('data.csv', sep = ',', usecols = [0, 1])\n", "for index, title, url in df.itertuples():\n", "    print(title)\n", "    print(url)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "def fetchUrl(url):\n", "    '''\n", "    发起网络请求\n", "    '''\n", "    headers = {\n", "        # 'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',\n", "        \"accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\",\n", "        # 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36',\n", "        \"user-agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0\",\n", "    }\n", "    r = requests.get(url,headers=headers)\n", "    r.raise_for_status()\n", "    r.encoding = \"utf-8\"\n", "    return r.text"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup\n", "\n", "def getContent(html):\n", "    '''\n", "    提取文章的正文部分的 html\n", "    '''\n", "    html = html.replace(\"&nbsp;\", \"\")\n", "    html = html.replace(\"~~\", \"~\").replace(\"~~\", \"~\")\n", "\n", "    bsObj = BeautifulSoup(html,'lxml')\n", "    title = bsObj.find(\"h1\").text\n", "    content = bsObj.find(\"div\",attrs = {\"id\":\"b_panel_schedule\"})\n", "\n", "    imgs = content.find_all(\"img\")\n", "    for img in imgs:\n", "        src = img['data-original']\n", "        txt = img['title']\n", "        img.insert_after(\"![{0}]({1})\".format(txt,src))\n", "        img.extract()\n", "\n", "    header5 = content.find_all(\"h5\")\n", "    for h5 in header5:\n", "        t5 = h5.find(\"div\", attrs = {\"class\":\"b_poi_title_box\"})\n", "        #print(t5.text)\n", "        h5.insert_after(\"##### \" + t5.text)\n", "        h5.extract()\n", "\n", "    cmts = content.find_all(\"div\", attrs = {\"class\":\"ops\"})\n", "    for s in cmts:\n", "        s.insert_after('< br/>')\n", "        s.extract()\n", "\n", "    return str(content)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:10: SyntaxWarning: invalid escape sequence '\\.'\n", "<>:10: SyntaxWarning: invalid escape sequence '\\.'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_24636\\*********.py:10: SyntaxWarning: invalid escape sequence '\\.'\n", "  text = text.replace(\"\\.\",\".\")\n"]}], "source": ["import html2text as ht\n", "\n", "def Html2Markdown(html):\n", "    '''\n", "    将文章正文部分由 html 格式转换成 Markdown 格式\n", "    '''\n", "    text_maker = ht.HTML2Text()\n", "    text = text_maker.handle(html)\n", "    text = text.replace(\"#\\n\\n\", \"# \")\n", "    text = text.replace(\"\\.\",\".\")\n", "    text = text.replace(\".\\n\",\". \")\n", "    text = text.replace(\"< br/>\",\"\\n\")\n", "    text = text.replace(\"tr-\\n\",\"tr-\")\n", "    text = text.replace(\"查看全部 __\",\"\")\n", "    return text"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "def saveMarkdownFile(title,content):\n", "    '''\n", "    保存文本到 Markdown 文件中\n", "    title：文件名\n", "    content：要保存的文本内容\n", "    '''\n", "    # 剔除或替换文件名中不允许出现的符号\n", "    title = title.replace(\"\\\\\", \"\")\n", "    title = title.replace(\"/\", \"\")\n", "    title = title.replace(\"\\\"\",\"”\")\n", "    title = title.replace(\"'\",\"’\")\n", "    title = title.replace(\"<\",\"《\")\n", "    title = title.replace(\">\",\"》\")\n", "    title = title.replace(\"|\",\"&\")\n", "    title = title.replace(\":\",\"：\")\n", "    title = title.replace(\"*\",\"x\")\n", "    title = title.replace(\"?\",\"？\")\n", "    \n", "    with open(\"data/\" + title + \".md\", 'w', encoding='utf-8') as f:\n", "        f.write(content)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "FeatureNotFound", "evalue": "Couldn't find a tree builder with the features you requested: lxml. Do you need to install a parser library?", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFeatureNotFound\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 19\u001b[39m\n\u001b[32m     16\u001b[39m         time.sleep(t)\n\u001b[32m     18\u001b[39m \u001b[38;5;66;03m# 启动爬虫\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m19\u001b[39m \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     20\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m爬取完成！\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 9\u001b[39m, in \u001b[36mmain\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m index, title, url \u001b[38;5;129;01min\u001b[39;00m df.itertuples():\n\u001b[32m      8\u001b[39m     html = fetchUrl(url)\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m     content = \u001b[43mgetContent\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhtml\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     10\u001b[39m     md = Html2Markdown(content)\n\u001b[32m     11\u001b[39m     saveMarkdownFile(title, md)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 10\u001b[39m, in \u001b[36mgetContent\u001b[39m\u001b[34m(html)\u001b[39m\n\u001b[32m      7\u001b[39m html = html.replace(\u001b[33m\"\u001b[39m\u001b[33m&nbsp;\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      8\u001b[39m html = html.replace(\u001b[33m\"\u001b[39m\u001b[33m~~\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33m~\u001b[39m\u001b[33m\"\u001b[39m).replace(\u001b[33m\"\u001b[39m\u001b[33m~~\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33m~\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m bsObj = \u001b[43mBeautifulSoup\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhtml\u001b[49m\u001b[43m,\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mlxml\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     11\u001b[39m title = bsObj.find(\u001b[33m\"\u001b[39m\u001b[33mh1\u001b[39m\u001b[33m\"\u001b[39m).text\n\u001b[32m     12\u001b[39m content = bsObj.find(\u001b[33m\"\u001b[39m\u001b[33mdiv\u001b[39m\u001b[33m\"\u001b[39m,attrs = {\u001b[33m\"\u001b[39m\u001b[33mid\u001b[39m\u001b[33m\"\u001b[39m:\u001b[33m\"\u001b[39m\u001b[33mb_panel_schedule\u001b[39m\u001b[33m\"\u001b[39m})\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\Program\\anaconda3\\envs\\webcrawl\\Lib\\site-packages\\bs4\\__init__.py:364\u001b[39m, in \u001b[36mBeautifulSoup.__init__\u001b[39m\u001b[34m(self, markup, features, builder, parse_only, from_encoding, exclude_encodings, element_classes, **kwargs)\u001b[39m\n\u001b[32m    362\u001b[39m     possible_builder_class = builder_registry.lookup(*features)\n\u001b[32m    363\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m possible_builder_class \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m364\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m FeatureNotFound(\n\u001b[32m    365\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mCouldn\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt find a tree builder with the features you \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    366\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mrequested: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m. Do you need to install a parser library?\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    367\u001b[39m             % \u001b[33m\"\u001b[39m\u001b[33m,\u001b[39m\u001b[33m\"\u001b[39m.join(features)\n\u001b[32m    368\u001b[39m         )\n\u001b[32m    369\u001b[39m     builder_class = possible_builder_class\n\u001b[32m    371\u001b[39m \u001b[38;5;66;03m# At this point either we have a TreeBuilder instance in\u001b[39;00m\n\u001b[32m    372\u001b[39m \u001b[38;5;66;03m# builder, or we have a builder_class that we can instantiate\u001b[39;00m\n\u001b[32m    373\u001b[39m \u001b[38;5;66;03m# with the remaining **kwargs.\u001b[39;00m\n", "\u001b[31mFeatureNotFound\u001b[39m: Couldn't find a tree builder with the features you requested: lxml. Do you need to install a parser library?"]}], "source": ["import time\n", "from random import randint\n", "\n", "def main():\n", "\n", "    df = pd.read_csv('data.csv', sep = ',', usecols = [0, 1])\n", "    for index, title, url in df.itertuples():\n", "        html = fetchUrl(url)\n", "        content = getContent(html)\n", "        md = Html2Markdown(content)\n", "        saveMarkdownFile(title, md)\n", "\n", "        # 随机等待时间，避免爬取过于频繁触发反爬机制\n", "        t = randint(0,10)\n", "        print(\"wait -- \",str(t),\"s\")\n", "        time.sleep(t)\n", "\n", "# 启动爬虫\n", "main()\n", "print(\"爬取完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "webcrawl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}