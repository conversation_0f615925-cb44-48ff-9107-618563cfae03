"""
从 logs 文件夹中，返回含以下字符的行

1. 达到最大重试次数，返回原始景点数据
2. API调用失败 (第 3 次)
"""

import os

def pick_log(log_path):
    """
    获取 scenery/logs 文件夹下所有log文件
    逐行读取文件内容，匹配获取含有特定字符的行
    """
    result = []

    # 确保是文件而非子目录
    if os.path.isfile(log_path):
        try:
            # 以UTF-8编码打开文件
            with open(log_path, 'r', encoding='utf-8') as f:
                for line in f:
                    # 判断是否包含目标字符串之一
                    if '达到最大重试次数，返回原始景点数据' in line or 'API调用失败 (第 3 次)' in line:
                        # 去除行首尾空白（如换行符）后加入结果列表
                        result.append(line.strip())
        except Exception as e:
            # 忽略无法读取的文件（如二进制文件或编码不兼容的文件）
            pass
    return result


result = pick_log("scenery/logs/scenery_process-1500.log")

with open("scenery/logs/result.txt", "w", encoding="utf-8") as f:
    for line in result:
        f.write(line + "\n")

record = []
with open("scenery/logs/result.txt", "r", encoding="utf-8") as f:
    for line in f:
        record.append()