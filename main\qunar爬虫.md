# 去哪儿网旅游游记爬虫项目文档


以下使用 `"""` 括起的内容不可修改：


"""
核心实现思路：
1. 爬取旅游攻略的 li 列表 ✔️。
   1. #TODO 未配置代理ip池
   2. 不重要的修改。如果存放数据的 csv 的表头错误创建，后面重新运行代码不会修改表头。可以每次生成完全新的 csv 文件避免这个问题。可以考虑增量爬取，也就是爬取标题信息后和已有csv文件中比对。也可以直接写入数据库持久化存储，数据库设置主键去除。

2. 爬取攻略的具体页面内容 ✔️。以 md 格式记录页面内容。
   1. 失败则写入日志文件。爬取500条数据。成功:364, 失败:136。原因未知。tmux中无法展示error的print。
   2. #TODO 照片没有下载到本地。图片链接中往往含有"img"和"jpg"。文件中 `travel.qunar.com/p-oi` 链接为一些景点内容。
   3. #TODO 读取 md 文件，去除所有语法字符，转为纯文本。

3. 非必要 爬取每个用户的所有游记记录。步骤1中新增一列爬取作者的href，然后根据csv中逐行读取作者href。
   1. 修改href，展示所有游记内容。https://travel.qunar.com/space/*********@qunar->https://travel.qunar.com/space/notes/*********@qunar
   2. 获取游记总数
   3. ul li中获取游记发表时间和标题
   4. 存入csv文件。格式：作者名，发表时间，游记标题

4. 爬取用户相关信息。关注数、粉丝数等

5. 其他功能
   1. #TODO 断点续爬，中断程序后，再次运行能继续爬取。主要针对 page_content.py 文件。每次完成某篇游记 md 文件下载，以及图片下载，那么在 data.csv 中增加标识 is_download，完成则为1。程序开始后会自动从第一个 is_download=0 的记录开始爬取


文件结构需要整理一下。

data/
- data_destinationa.csv  # 标题	游记id 游记链接	作者名	作者id	作者游记页链接	景点	点赞数	正文	图片文件夹名（游记id/）
- data_destination2.csv

汇总所有csv文件到data.csv

- destination
   - 游记id1.md
   - 游记id2.md
- img/游记id/  # 图片文件夹


优先走通整个流程，再考虑优化
"""

## 1. 项目架构概览

该项目是一个针对去哪儿网(Qunar)旅游游记的爬虫程序，主要功能是抓取旅游攻略并将数据保存到CSV文件中。项目采用Python语言编写，使用了多种反爬虫策略，包括：
- 随机User-Agent轮换
- 代理IP池使用与轮换
- 请求间隔随机延时
- 请求失败重试机制

整体架构采用单文件模块化设计，通过函数封装不同功能单元，遵循数据获取、解析、存储的基本爬虫工作流程。

## 2. 主要目录结构及其职责

由于项目主要集中在单个Python文件中，目录结构相对简单：

```
scrape/
├── demo.py         # 主要爬虫程序
├── data.csv        # 爬取数据的输出文件
├── qunar.html      # 网页HTML样本文件(用于开发测试)
├── qunar1.html     # 网页HTML样本文件(用于开发测试)
├── proxy_pool/     # 代理池相关代码或配置(注释中提到但未实际使用)
└── data/           # 可能存放其他数据文件
```

## 3. 关键模块的依赖关系图

```
                  +------------+
                  |   入口点    |
                  | main函数调用 |
                  +------+-----+
                         |
                         v
              +----------+-----------+
              |  downloadBookInfo   |
              | 控制整体爬取流程     |
              +----------+-----------+
                         |
           +-------------+-------------+
           |             |             |
           v             v             v
  +--------+---+  +------+-----+  +---+--------+
  | fetchHotel |  | getPageNum |  | parseHtml  |
  | 获取页面内容 |  | 获取总页数  |  | 解析网页数据 |
  +--------+---+  +------------+  +---+--------+
           |                           |
           v                           v
  +--------+---+                +------+-----+
  |get_working_|                | saveCsvFile|
  |   proxy    |                | 保存到CSV  |
  +--------+---+                +------------+
           |
           v
  +--------+---+
  | test_proxy |
  | 测试代理可用 |
  +------------+
```

## 4. 核心类和接口的功能说明

项目使用函数式编程风格，主要函数及其功能如下：

| 函数名 | 功能描述 |
|-------|---------|
| `test_proxy(proxy)` | 测试一个代理是否可用，通过尝试访问百度网站来验证 |
| `get_working_proxy()` | 从代理池中获取一个可用的代理，随机打乱代理列表顺序尝试 |
| `fetchHotel(url)` | 发送HTTP请求获取指定URL的网页内容，包含随机延时、随机UA、代理使用和失败重试机制 |
| `getPageNum(html)` | 从HTML中解析出总页数，用于确定需要爬取的页面数量 |
| `parseHtml(html)` | 解析HTML内容，提取游记数据，使用生成器yield返回结果 |
| `saveCsvFile(filename, content)` | 将提取的数据保存到CSV文件中，以追加模式写入 |
| `downloadBookInfo(url, fileName)` | 主控函数，协调整个爬取流程，包括多页面爬取控制和数据流向管理 |

## 5. 数据流向图

```
网络请求 ──> HTML内容 ──> 解析数据 ──> CSV存储
   │           │           │
   │           │           │
   v           v           v
随机UA       页面解析     数据转换
代理IP       页码提取    
随机延时     数据提取
重试机制
```

详细流程：
1. 通过`fetchHotel`函数发送网络请求获取HTML
2. 通过`getPageNum`函数解析总页数
3. 循环爬取每一页内容
4. 每页通过`parseHtml`函数解析出游记数据
5. 解析出的数据通过`saveCsvFile`函数保存到CSV文件中

## 6. API接口清单

项目主要针对去哪儿网的以下接口进行爬取：

| 接口URL | 描述 | 参数 |
|--------|------|-----|
| `https://travel.qunar.com/travelbook/list/上海/hot_heat/{page}.htm` | 获取上海地区热门游记列表的第{page}页 | page: 页码 |

## 7. 常见的代码模式和约定

1. **错误处理模式**：
   - 使用try-except捕获网络请求异常
   - 请求失败时实现了最多3次重试机制
   - 所有代理均不可用时自动降级为直连
   
2. **反爬策略**：
   - 随机User-Agent轮换：从预定义的6种UA中随机选择
   - 请求延时：每次请求前随机延时5-10秒
   - 页面间隔：每个页面爬取间隔8-12秒
   - 代理IP使用：使用7个预定义的代理IP
   
3. **数据处理约定**：
   - CSV文件追加模式写入，避免数据丢失
   - 提取的字段保持固定顺序：标题、链接、作者等12个字段
   - 对可能不存在的字段进行空值处理
   
4. **限流保护**：
   - 硬编码限制最大爬取页数为4页，防止过度爬取
   - 函数中内置随机延时

5. **代码注释风格**：
   - 使用双引号作为多行注释
   - 函数注释使用三引号文档字符串
   - 行内注释使用井号并保持一致的缩进样式 


## 8. CSV文件重复行问题及解决方案

### 8.1 问题描述

在爬虫代码早期版本中，生成的CSV文件存在以下问题：
- 重复的标题行：CSV文件中会出现多个标题行，导致数据结构混乱
- 重复的数据行：相同的游记数据会被重复写入文件

### 8.2 问题原因分析

问题主要出现在数据写入机制上：

1. **标题行重复的原因**：
   - 每次运行程序都会无条件写入一次标题行
   - 使用追加模式(`mode="a"`)时，之前的文件内容保留，导致标题行累积
   - 缺少文件存在性检查，未区分首次创建和后续追加操作

2. **数据行重复的原因**：
   - `parseHtml`函数使用生成器模式(`yield`)逐条返回数据
   - 每次yield操作都创建新的数据列表，而非累积到一个列表中
   - 缺少数据唯一性校验，允许重复内容写入

3. **流程控制问题**：
   - 未对数据写入过程进行全局控制和监督
   - 未区分首次写入和追加写入的逻辑处理差异

### 8.3 解决方案

针对上述问题，采取以下解决方案：

1. **解决标题行重复**：
   - 使用`os.path.isfile`检查文件是否存在
   - 仅在文件不存在时创建新文件并写入标题行
   - 后续写入时明确指定`header=False`，确保不再写入标题

2. **解决数据行重复**：
   - 修改`parseHtml`函数，从生成器模式改为返回完整数据列表
   - 每个页面的数据作为整体处理和写入，减少写入频率
   - 采用DataFrame批量写入，提高效率并减少文件操作

3. **优化流程控制**：
   - 区分首页和后续页面的数据处理逻辑
   - 添加数据统计和日志输出，便于监控进度和排查问题
   - 简化数据流转路径，减少中间环节

### 8.4 CSV文件写入最佳实践

基于此次问题的解决经验，总结以下CSV文件写入的最佳实践：

1. **文件存在性检查**：
   ```python
   import os
   
   # 检查文件是否存在
   file_exists = os.path.isfile(filename)
   
   # 根据文件存在情况采取不同策略
   if not file_exists:
       # 创建新文件并写入标题
       df = pd.DataFrame(columns=columns)
       df.to_csv(filename, encoding="utf_8_sig", index=False)
   ```

2. **区分首次写入和追加写入**：
   ```python
   # 首次写入（带标题）
   df.to_csv(filename, encoding="utf_8_sig", mode="w", index=False)
   
   # 追加写入（不带标题）
   df.to_csv(filename, encoding="utf_8_sig", mode="a", index=False, header=False)
   ```


3. **通用CSV写入函数模板**：
   ```python
   import os
   import pandas as pd
   def save_to_csv(filename, data, columns=None, append=True):
       """
       通用CSV文件保存函数
       
       Parameters:
       -----------
       filename : str
           CSV文件路径
       data : list
           要保存的数据列表
       columns : list, optional
           列名列表，默认为None
       append : bool, optional
           是否追加模式，默认为True
       """
       
       # 转换为DataFrame
       df = pd.DataFrame(data, columns=columns)
       
       # 检查文件是否存在
       file_exists = os.path.isfile(filename)
       
       if append and file_exists:
           # 追加模式
           df.to_csv(filename, encoding="utf_8_sig", mode="a", 
                     index=False, header=False)
       else:
           # 新建或覆盖模式
           df.to_csv(filename, encoding="utf_8_sig", mode="w", 
                     index=False, header=bool(columns))
       
       return len(data)  # 返回写入的记录数
   ```

通过采用以上最佳实践，可以有效避免CSV文件写入过程中出现的重复行问题，确保数据的完整性和一致性。 

## 9. 日志记录和错误处理最佳实践

在爬虫项目中，适当的日志记录和错误处理对于监控爬虫运行状态、排查问题和提高成功率至关重要。以下是完整的日志记录和错误处理模板。

### 9.1 日志记录模板

以下是一个可重用的日志配置模板，适用于爬虫项目：

```python
import logging
import datetime
import os

def setup_logger():
    """
    配置日志记录器，同时将日志输出到文件和控制台
    
    Returns:
    --------
    logger: logging.Logger
        配置好的日志记录器实例
    """
    # 创建日志目录
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # 使用时间戳创建唯一的日志文件名
    log_filename = f"{log_dir}/scraper_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,  # 日志级别
        format='%(asctime)s - %(levelname)s - %(message)s',  # 日志格式
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),  # 文件处理器
            logging.StreamHandler()  # 控制台处理器
        ]
    )
    
    return logging.getLogger()

# 使用示例
logger = setup_logger()
logger.info("爬虫程序启动")
logger.warning("发现潜在问题")
logger.error("遇到错误")
logger.critical("遇到严重错误")
```

### 9.2 记录爬虫关键阶段

在爬虫程序中应记录以下关键阶段：

```python
# 程序启动与结束
logger.info("爬虫程序启动")
logger.info(f"爬取完成! 成功: {success_count}, 失败: {failed_count}")

# 网络请求
logger.info(f"正在请求: {url}")
logger.info(f"使用User-Agent: {user_agent}")
logger.info(f"等待 {sleep_time:.2f} 秒...")

# 数据解析
logger.info(f"正在解析第 {page_num} 页数据")
logger.info(f"在页面中发现 {item_count} 条记录")

# 数据保存
logger.info(f"成功保存文件: {filename}")

# 异常情况
logger.error(f"HTTP错误: {e}, URL: {url}, 状态码: {e.response.status_code}")
logger.error(f"连接错误: {e}, URL: {url}")
logger.error(f"请求超时: {e}, URL: {url}")
logger.warning(f"处理图片时出错: {e}, 文章: {title}")
```

### 9.3 记录失败项并保存到CSV

为了便于后续分析和重试失败项，建议将失败记录保存到专门的CSV文件：

```python
import pandas as pd

# 创建失败项记录列表
failed_items = []

# 在爬取过程中记录失败项
if not html:
    error_msg = f"URL获取失败"
    logger.error(f"跳过处理: {title}, {error_msg}")
    failed_items.append({"title": title, "url": url, "reason": error_msg})
    failed_count += 1
    continue

# 爬取结束后保存失败项到CSV
if failed_items:
    failed_df = pd.DataFrame(failed_items)
    failed_file = f"failed_items_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    failed_df.to_csv(failed_file, index=False, encoding='utf-8')
    logger.info(f"已将失败项保存至: {failed_file}")
```

### 9.4 URL自动替换策略

针对不同网站的页面路径可能发生变化的情况，可以实现URL自动替换机制，提高爬取成功率：

```python
def try_alternative_urls(url, title):
    """
    尝试多种URL变体以增加获取成功率
    
    Parameters:
    -----------
    url : str
        原始URL
    title : str
        内容标题（用于日志记录）
        
    Returns:
    --------
    html : str or None
        成功获取的HTML内容或None
    success_url : str
        成功获取内容的URL
    """
    # 首先尝试原始URL
    logger.info(f"尝试获取内容: {title}, URL: {url}")
    html = fetchUrl(url)
    
    if html:
        return html, url
    
    # 替换规则列表 - 可根据网站特性扩展
    replace_rules = [
        ("youji", "travelbook/note"),
        ("travel", "lvyou"),
        ("note", "article"),
        # 可添加更多替换规则
    ]
    
    # 尝试各种URL变体
    for old, new in replace_rules:
        if old in url:
            new_url = url.replace(old, new)
            logger.info(f"原始URL获取失败，尝试替换URL: {new_url}")
            html = fetchUrl(new_url)
            if html:
                logger.info(f"使用替换后的URL成功获取内容: {new_url}")
                return html, new_url
    
    # 所有尝试都失败
    logger.error(f"所有URL变体都无法获取内容: {title}")
    return None, url

# 使用示例
html, final_url = try_alternative_urls(url, title)
if not html:
    error_msg = "所有URL尝试均失败"
    logger.error(f"跳过处理: {title}, {error_msg}")
    failed_items.append({"title": title, "url": final_url, "reason": error_msg})
    continue
```

### 9.5 综合实现示例

将上述所有功能整合到爬虫主体逻辑中，形成完整的错误处理和日志记录体系：

```python
def main():
    logger.info("开始爬取...")
    try:
        df = pd.read_csv('data.csv', sep=',', usecols=[0, 1])
        success_count = 0
        failed_count = 0
        failed_items = []
        
        for index, title, url in df.itertuples():
            logger.info(f"\n正在处理 [{index+1}/{len(df)}]: {title}")
            try:
                # 尝试各种URL变体
                html, final_url = try_alternative_urls(url, title)
                
                if not html:
                    error_msg = "所有URL尝试均失败"
                    logger.error(f"跳过处理: {title}, {error_msg}")
                    failed_count += 1
                    failed_items.append({"title": title, "url": final_url, "reason": error_msg})
                    continue
                
                # 解析内容
                content = getContent(html, title)
                if not content:
                    error_msg = f"内容提取失败"
                    logger.error(f"跳过处理: {title}, {error_msg}")
                    failed_count += 1
                    failed_items.append({"title": title, "url": final_url, "reason": error_msg})
                    continue
                
                # 保存内容
                saveContent(title, content)
                success_count += 1
                logger.info(f"成功处理: {title}")

                # 随机等待
                t = randint(5, 10)
                logger.info(f"等待 {t} 秒后继续...")
                time.sleep(t)
                
            except Exception as e:
                error_msg = f"处理异常: {str(e)}"
                logger.error(f"{error_msg}, 文章: {title}")
                failed_count += 1
                failed_items.append({"title": title, "url": url, "reason": error_msg})
                continue
                
        # 保存统计结果
        logger.info(f"\n爬取完成! 成功: {success_count}, 失败: {failed_count}")
        
        # 保存失败项
        if failed_items:
            failed_df = pd.DataFrame(failed_items)
            failed_file = f"failed_items_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            failed_df.to_csv(failed_file, index=False, encoding='utf-8')
            logger.info(f"已将失败项保存至: {failed_file}")
            
    except Exception as e:
        logger.critical(f"程序执行出错: {e}")
```

通过以上日志记录和错误处理模板，可以使爬虫程序更加健壮，异常更加可控，排障和维护更加高效。 