"""
测试修复后的景点处理系统
专门测试第17行记录的问题
"""

import re

def format_output(scenery_final: str) -> bool:
    """
    目的：判断 combine_scenry() 函数的输出格式是否符合要求。"景点1|景点2|景点3"，优化后更宽松
    输入：待判断格式的str
    输出：True or False
    """
    if not scenery_final or scenery_final.strip() == "":
        return True  # 空字符串也是合法的

    # 去除首尾空白字符和可能的首尾"|"
    cleaned = scenery_final.strip().strip('|')

    if not cleaned:
        return True  # 清理后为空也是合法的

    # 检查是否没有连续的"|"
    if '||' in scenery_final:
        return False

    # 分割景点并检查每个景点
    spots = [spot.strip() for spot in cleaned.split('|') if spot.strip()]

    if not spots:
        return True  # 没有有效景点也是合法的

    # 检查每个景点名称
    for spot in spots:
        # 允许中文、数字、常见标点符号
        # 特殊处理：允许一些英文字母（如798艺术区、K11等）
        # 不允许+号，确保景点之间只用|分隔
        if not re.match(r'^[\u4e00-\u9fff\d\w（）()·\-\s]+$', spot):
            return False

        # 景点名称不能太短或太长
        if len(spot) < 1 or len(spot) > 25:  # 稍微放宽长度限制
            return False

    return True


def simple_fallback_combine(all_spots, index):
    """
    备用景点合并方案，当LLM处理失败时使用
    进行简单的去重和基本清理
    """
    if not all_spots:
        return ""

    # 处理包含+号的景点名称
    processed_spots = []
    for spot in all_spots:
        spot = spot.strip()
        if '+' in spot:
            # 如果包含+号，分割成多个景点
            parts = [part.strip() for part in spot.split('+') if part.strip()]
            processed_spots.extend(parts)
            print(f"第{index + 1}行记录：处理+号分割：{spot} -> {parts}")
        else:
            processed_spots.append(spot)

    # 简单去重，保持顺序
    seen = set()
    unique_spots = []
    for spot in processed_spots:
        if spot and spot not in seen:
            seen.add(spot)
            unique_spots.append(spot)

    # 基本的非景点过滤
    filtered_spots = []
    exclude_keywords = ['酒店', '宾馆', '餐厅', '餐馆', '食府', '酒家', '商场', '购物中心', '机场', '火车站', '汽车站']

    for spot in unique_spots:
        # 检查是否包含明显的非景点关键词
        is_excluded = False
        for keyword in exclude_keywords:
            if keyword in spot:
                is_excluded = True
                break

        if not is_excluded:
            filtered_spots.append(spot)

    result = '|'.join(filtered_spots)
    print(f"第{index + 1}行记录：备用处理完成，结果：{result}")
    return result

def test_format_output():
    """测试格式验证函数"""
    print("=== 测试格式验证函数 ===")

    # 测试包含+号的景点名称（现在应该被拒绝）
    test_cases = [
        "阳江|佛山|广州|马尾岛|海陵岛|武术表演+黄飞鸿纪念馆|广州塔小蛮腰",  # 包含+号，应该失败
        "故宫|天安门|王府井",  # 正常格式，应该成功
        "798艺术区|K11购物中心",  # 包含英文数字，应该成功
        "",  # 空字符串，应该成功
        "景点1|景点2||景点3",  # 连续分隔符，应该失败
        "很长很长很长很长很长很长很长很长很长很长的景点名称"  # 超长名称，应该成功
    ]

    for case in test_cases:
        result = format_output(case)
        print(f"输入: {case}")
        print(f"验证结果: {result}")
        print("-" * 50)

def test_fallback_combine():
    """测试备用合并函数"""
    print("\n=== 测试备用合并函数 ===")

    # 测试包含+号的情况
    test_spots_with_plus = [
        "阳江", "佛山", "广州", "马尾岛", "海陵岛",
        "武术表演+黄飞鸿纪念馆",  # 包含+号的景点
        "华盖路步行街", "岭南天地",
        "十里银滩", "红树林国家湿地公园",
        "阳江北洛秘境度假酒店",  # 应该被过滤的酒店
        "点都德茶餐厅"  # 应该被过滤的餐厅
    ]

    print("测试包含+号的景点处理：")
    result = simple_fallback_combine(test_spots_with_plus, 17)
    print(f"输入景点: {test_spots_with_plus}")
    print(f"备用处理结果: {result}")

    print("\n" + "="*50)

    # 测试正常情况
    test_spots_normal = [
        "阳江", "佛山", "广州", "马尾岛", "海陵岛",
        "华盖路步行街", "岭南天地",
        "黄飞鸿纪念馆", "十里银滩", "红树林国家湿地公园"
    ]

    print("测试正常景点处理：")
    result2 = simple_fallback_combine(test_spots_normal, 18)
    print(f"输入景点: {test_spots_normal}")
    print(f"备用处理结果: {result2}")

if __name__ == "__main__":
    test_format_output()
    test_fallback_combine()
