#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的景点处理系统
去掉备用处理，完全依赖LLM进行景点标准化
"""

import pandas as pd
import logging
from pathlib import Path
import sys

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

# 导入处理函数
from processScenery_copy_2 import main, combine_scenery, format_output

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('test_improved.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def test_format_output():
    """测试格式验证函数"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试格式验证函数 ===")
    
    test_cases = [
        ("广州|佛山|珠海", True),
        ("故宫|天安门|王府井", True),
        ("", True),
        ("广州", True),
        ("广州||佛山", False),
        ("广州|佛山|", True),
        ("|广州|佛山", True),
        ("798艺术区|K11", True),
        ("广州|佛山|珠海|光孝寺|白云山", True),
    ]
    
    for test_input, expected in test_cases:
        result = format_output(test_input)
        status = "✓" if result == expected else "✗"
        logger.info(f"{status} 输入: '{test_input}' -> {result} (期望: {expected})")

def test_combine_scenery():
    """测试景点合并函数"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试景点合并函数 ===")
    
    test_cases = [
        {
            "index": 0,
            "scenery": "广州|佛山|珠海",
            "extract": "故宫博物院|天安门广场",
            "description": "基本合并测试"
        },
        {
            "index": 1,
            "scenery": "",
            "extract": "颐和园景区|天坛公园",
            "description": "只有提取结果"
        },
        {
            "index": 2,
            "scenery": "北京|上海",
            "extract": "",
            "description": "只有原始数据"
        },
        {
            "index": 3,
            "scenery": "",
            "extract": "",
            "description": "都为空"
        }
    ]
    
    for case in test_cases:
        logger.info(f"\n--- {case['description']} ---")
        logger.info(f"原始景点: {case['scenery']}")
        logger.info(f"提取景点: {case['extract']}")
        
        try:
            result, method = combine_scenery(
                case['index'], 
                case['scenery'], 
                case['extract']
            )
            logger.info(f"结果: {result}")
            logger.info(f"方法: {method}")
        except Exception as e:
            logger.error(f"处理失败: {e}")

def test_small_dataset():
    """测试小数据集"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试小数据集处理 ===")
    
    # 创建测试数据
    test_data = {
        'title': [
            '广州佛山珠海三日游',
            '北京故宫天安门一日游',
            '上海外滩东方明珠游记'
        ],
        'scenery': [
            '广州|佛山|珠海',
            '北京',
            '上海'
        ],
        'processed_text': [
            '今天去了光孝寺和白云山，还参观了佛山祖庙',
            '参观了故宫博物院，在天安门广场拍照，逛了王府井大街',
            '在外滩看夜景，登上了东方明珠电视塔'
        ]
    }
    
    df = pd.DataFrame(test_data)
    test_csv_path = 'test_data.csv'
    test_output_path = 'test_output.csv'
    
    # 保存测试数据
    df.to_csv(test_csv_path, index=False, encoding='utf-8')
    logger.info(f"创建测试数据文件: {test_csv_path}")
    
    try:
        # 运行处理
        main(test_csv_path, test_output_path)
        
        # 检查结果
        if Path(test_output_path).exists():
            result_df = pd.read_csv(test_output_path, encoding='utf-8')
            logger.info("=== 处理结果 ===")
            for i, row in result_df.iterrows():
                logger.info(f"第{i+1}行:")
                logger.info(f"  原始: {row.get('scenery', 'N/A')}")
                logger.info(f"  最终: {row.get('scenery_final', 'N/A')}")
                logger.info(f"  方法: {row.get('combine_method', 'N/A')}")
                logger.info(f"  提取: {row.get('scenery_tmp', 'N/A')}")
        else:
            logger.error("输出文件未生成")
            
    except Exception as e:
        logger.error(f"测试处理失败: {e}")
    finally:
        # 清理测试文件
        for file_path in [test_csv_path, test_output_path]:
            if Path(file_path).exists():
                Path(file_path).unlink()
                logger.info(f"清理文件: {file_path}")

def main_test():
    """主测试函数"""
    logger = setup_logging()
    logger.info("开始测试改进后的景点处理系统")
    
    try:
        # 测试格式验证
        test_format_output()
        
        # 测试景点合并
        test_combine_scenery()
        
        # 测试小数据集
        test_small_dataset()
        
        logger.info("所有测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main_test()
