{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["去重 data.csv 文件\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在读取文件: data.csv\n", "原始数据行数: 51\n", "去重后行数: 42\n", "删除了 9 行重复数据\n", "去重后的数据已保存到: data.csv\n", "\n", "去重后的前5行数据:\n", "                                      标题  \\\n", "0                              上海四晚五日亲子游   \n", "1  上海迪士尼2日游图文攻略不买尊享卡狂刷12主项+3大节目+22个必看小提示   \n", "2                   周末，用美食奖赏自己—那些我爱的上海馆子   \n", "3                    遇见迪士尼 —— 炎炎夏日里的三日狂欢   \n", "4                            发现徐家汇，钟情小马路   \n", "\n", "                                       链接            作者           出发日期   天数  \\\n", "0  https://travel.qunar.com/youji/6815261         去哪儿用户  2017-04-27 出发  共5天   \n", "1  https://travel.qunar.com/youji/7672840       bjsmart  2021-04-15 出发  共3天   \n", "2  https://travel.qunar.com/youji/6591970         去哪儿用户  2015-12-01 出发  共1天   \n", "3  https://travel.qunar.com/youji/6906928  babyblue3732  2017-08-11 出发  共3天   \n", "4  https://travel.qunar.com/youji/7000995           许你忆  2018-03-03 出发  共1天   \n", "\n", "      照片数    人数                  玩法       费用    阅读数  点赞数 评论数  \n", "0  685张照片    亲子                短途周末  人均5000元  10.8万  296  29  \n", "1  264张照片    家庭  购物 穷游 美食 踏春 探险 第一次  人均4000元   4.4万  125  29  \n", "2  435张照片  独自一人          购物 美食 短途周末      NaN   8.9万  168  15  \n", "3   89张照片    家庭                  夏季      NaN     2万   71  14  \n", "4  318张照片  独自一人            摄影 徒步 人文    人均50元   1.6万   10   1  \n"]}], "source": ["import pandas as pd\n", "\n", "def remove_duplicates_from_csv(input_file, output_file=None):\n", "    \"\"\"\n", "    读取CSV文件并去除重复行\n", "    \n", "    参数:\n", "    input_file (str): 输入CSV文件路径\n", "    output_file (str, optional): 输出CSV文件路径，默认为None，将覆盖原文件\n", "    \n", "    返回:\n", "    DataFrame: 去重后的数据\n", "    \"\"\"\n", "    # 如果未指定输出文件，则覆盖原文件\n", "    if output_file is None:\n", "        output_file = input_file\n", "    \n", "    # 读取CSV文件\n", "    print(f\"正在读取文件: {input_file}\")\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 显示原始行数\n", "    original_rows = len(df)\n", "    print(f\"原始数据行数: {original_rows}\")\n", "    \n", "    # 去除重复行\n", "    df_no_duplicates = df.drop_duplicates()\n", "    \n", "    # 显示去重后的行数\n", "    new_rows = len(df_no_duplicates)\n", "    print(f\"去重后行数: {new_rows}\")\n", "    print(f\"删除了 {original_rows - new_rows} 行重复数据\")\n", "    \n", "    # 保存到CSV文件\n", "    df_no_duplicates.to_csv(output_file, index=False)\n", "    print(f\"去重后的数据已保存到: {output_file}\")\n", "    \n", "    return df_no_duplicates\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 指定输入文件\n", "    input_csv = \"data.csv\"\n", "    \n", "    # 可以选择指定输出文件，也可以覆盖原文件\n", "    # output_csv = \"data_no_duplicates.csv\"  # 指定新文件名\n", "    output_csv = input_csv  # 覆盖原文件\n", "    \n", "    # 执行去重操作\n", "    df_result = remove_duplicates_from_csv(input_csv, output_csv)\n", "    \n", "    # 可以查看部分去重后的数据\n", "    print(\"\\n去重后的前5行数据:\")\n", "    print(df_result.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["批量或单个验证代理ip是否可用"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["代理 https://*************:17841 不可用\n", "代理 https://*************:6379 不可用\n", "代理 https://*************:22753 不可用\n", "代理 http://************:80 不可用\n", "代理 http://************:13001 不可用\n", "代理 http://************:80 不可用\n", "\n", "测试结果：\n", "可用代理列表: []\n", "不可用代理列表: ['https://*************:17841', 'https://*************:6379', 'https://*************:22753', 'http://************:80', 'http://************:13001', 'http://************:80']\n"]}], "source": ["import requests\n", "\n", "def test_proxy(proxy_url, target_url):\n", "    proxies = {\n", "        \"http\": proxy_url,\n", "        \"https\": proxy_url\n", "    }\n", "\n", "    try:\n", "        response = requests.get(target_url, proxies=proxies, timeout=10)\n", "        if response.status_code == 200:\n", "            return True\n", "        else:\n", "            return False\n", "    except requests.exceptions.RequestException:\n", "        return False\n", "\n", "\n", "def test_proxy_list(proxy_url_list, target_url):\n", "    available_proxies = []  # 存储可用的代理\n", "    unavailable_proxies = []  # 存储不可用的代理\n", "\n", "    for proxy in proxy_url_list:\n", "        if test_proxy(proxy, target_url):\n", "            print(f\"代理 {proxy} 可用\")\n", "            available_proxies.append(proxy)\n", "        else:\n", "            print(f\"代理 {proxy} 不可用\")\n", "            unavailable_proxies.append(proxy)\n", "\n", "    return available_proxies, unavailable_proxies\n", "\n", "\n", "# 使用示例\n", "# proxy_url_list = [\n", "#     \"https://58.209.137.128:8089\",\n", "#     \"https://**********:3128\",\n", "#     \"http://106.38.26.22:2080\",\n", "#     \"http://**************:4999\",\n", "#     \"http://218.77.183.214:5224\",\n", "#     \"http://8.219.97.248:80\",\n", "#     \"http://80.249.112.162:80\"\n", "# ]\n", "\n", "proxylist = [\n", "    'https://*************:17841',\n", "    'https://*************:6379',\n", "    'https://*************:22753',\n", "    'http://************:80',\n", "    'http://************:13001',\n", "    'http://************:80'\n", "]\n", "\n", "\n", "target_url = \"https://travel.qunar.com/travelbook/list.htm?order=hot_heat\"\n", "\n", "available, unavailable = test_proxy_list(proxylist, target_url)\n", "\n", "print(\"\\n测试结果：\")\n", "print(\"可用代理列表:\", available)\n", "print(\"不可用代理列表:\", unavailable)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import time\n", "import random\n", "import requests\n", "\n", "def fetchUrl(url):\n", "    '''发起网络请求'''\n", "\n", "    # 添加随机延时，模拟人类浏览行为\n", "    sleep_time = random.uniform(8, 12)  # 增加延时范围\n", "\n", "    print(f\"等待 {sleep_time:.2f} 秒...\")\n", "    time.sleep(sleep_time)\n", "\n", "    # 随机选择一个User-Agent\n", "    user_agent = \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36\"\n", "    print(f\"使用User-Agent: {user_agent}\")\n", "\n", "    headers = {\n", "        \"accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\",\n", "        \"user-agent\": user_agent,\n", "    }\n", "    try:\n", "        r = requests.get(url, headers=headers)\n", "        r.raise_for_status()\n", "        r.encoding = \"utf-8\"\n", "        return r.text\n", "    except requests.exceptions.HTTPError as e:\n", "        print(f\"HTTP错误: {e}, URL: {url}, 状态码: {e.response.status_code}\")\n", "    except requests.exceptions.ConnectionError as e:\n", "        print(f\"连接错误: {e}, URL: {url}\")\n", "    except requests.exceptions.Timeout as e:\n", "        print(f\"请求超时: {e}, URL: {url}\")\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"请求异常: {e}, URL: {url}\")\n", "    return None"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["等待 10.52 秒...\n", "使用User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36\n"]}], "source": ["html = fetchUrl(\"https://travel.qunar.com/travelbook/note/6906928\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\\n<html xmlns=\"http://www.w3.org/1999/xhtml\" style=\"padding-right: 0px;\">\\n<head>\\n\\t<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\\n\\t<meta name=\"keywords\" content=\"旅游攻略,旅游攻略下载,制作旅游攻略,景点查询,游记下载,游记分享,旅游日记,驴友攻略下载,去哪儿旅游攻略\">\\n    <meta name=\"description\" content=\"去哪儿网旅行频道提供在线旅游攻略制作、分享、查询，同时为用户提供多版本旅游攻略下载。景点攻略、美食攻略、购物攻略、住宿交通攻略等应有尽有，还有旅游行程管理、提醒服务。“Qunar.com 聪明你的旅行”\">\\n    <title>非常抱歉，您访问的页面不存在-去哪儿网Qunar.com</title>\\n \\t<link href=\"//q.qunarzz.com/travel_new_plan/prd/styles/css/<EMAIL>\" type=\"text/css\" rel=\"stylesheet\">    <script type=\"text/javascript\">\\t\\r\\n      var QZZ_TRAVEL_MODULES = {\\r\\n                 \"usercard\" : [\"//q.qunarzz.com/travel_new_plan/prd/scripts/common/<EMAIL>\"],\\r\\n                 \"download\" : [\"//q.qunarzz.com/travel_new_plan/prd/styles/css/<EMAIL>\",\\r\\n                              \"//q.qunarzz.com/travel_new_plan/prd/scripts/common/<EMAIL>\"],\\r\\n                 \"login_panel\" : [\"//q.qunarzz.com/travel_new_plan/prd/scripts/common/<EMAIL>\"],\\r\\n                 \"adcommon\" : [\"//q.qunarzz.com/travel_new_plan/prd/styles/css/<EMAIL>\",\\r\\n                               \"//q.qunarzz.com/travel_new_plan/prd/scripts/common/<EMAIL>\"]\\r\\n         };\\r\\n</script>\\r\\n<!--[if IE 6]>\\n        <script type=\"text/javascript\" src=\"//q.qunarzz.com/js/compatibilityUtils/build/DD_belatedPNG-min.js\">\\n        </script>\\n        <script type=\"text/javascript\">\\n          DD_belatedPNG.fix(\\'.pngfix\\');\\n        </script>\\n    <![endif]-->\\n</head>\\n<body>\\n\\t<!--- 动态底纹 -->\\n\\t<abbr style=\"display: none;\" data-type=\"qde\" id=\"home_active_dynamic\" data-query=\"chan=travel&pg=home&site=qunar&type=dynamic_skinned\" data-style=\"width:100%;\"></abbr>\\n\\t<link data-hfstamp=\"20250116162035\" id=\"hf-style\" data-hffile=\"header_travelbook_styles\" rel=\"stylesheet\" href=\"//common.qunarzz.com/hf_qzz/prd/styles/travel/<EMAIL>\">\\n<div class=\"q_header  q_header_travel q_header_travel_travelbook \">\\n\\t\\t<div class=\"q_header_main qunar-assist-screen-nav\" data-hfchannel=\"travel\" data-hffile=\"header_main_home\"> <div class=\"q_header_logo\" tabindex=\"-1\"> <a href=\"http://www.qunar.com/\" target=\"_top\" title=\"去哪儿旅游搜索引擎 Qunar.com\" hidefocus=\"on\" aria-label=\"返回首页\" tabindex=\"0\"><img style=\"height: 54px;\" src=\"https://s.qunarzz.com/f_cms/2021/1638865973476_447461838.png\" tabindex=\"-1\" alt=\"去哪儿旅游搜索引擎 Qunar.com\"></a> </div> <div class=\"q_header_tnav\" tabindex=\"-1\"> <ul tabindex=\"-1\"> <li><a href=\"javascript:void(0)\" onclick=\"aria.start()\" style=\"color:#333;text-decoration:none;position:relative;\" class=\"q_header_barrier_free\">适老化及无障碍</a></li> <li id=\"__loginInfo_u__\" class=\"q_header_username\" tabindex=\"-1\"><a href=\"//user.qunar.com/passport/login.jsp\" hidefocus=\"on\" rel=\"nofollow\" tabindex=\"0\">登录</a></li> <li id=\"__loginInfo_r__\" class=\"q_header_register\" tabindex=\"-1\"><a href=\"//user.qunar.com/passport/register.jsp\" hidefocus=\"on\" rel=\"nofollow\" tabindex=\"0\">注册</a></li> <li id=\"__loginInfo_t__\" class=\"q_header_tnav_menu qunar-assist-hide\" style=\"display:block;\"> <dl id=\"__loginInfo_l__\"> <dt><a href=\"//travel.qunar.com/space/\">我的空间</a><b></b></dt> <dd><a href=\"//travel.qunar.com/space/books/list\" hidefocus=\"on\" rel=\"nofollow\">我的行程</a></dd> <dd><a href=\"//travel.qunar.com/space/notes/list\" hidefocus=\"on\" rel=\"nofollow\" data-headerclk=\"Youji-onedaohang\">我的游记</a></dd> <dd><a href=\"//travel.qunar.com/space/bookcollection/list\" hidefocus=\"on\" rel=\"nofollow\">我的收藏</a></dd> <dd><a href=\"//travel.qunar.com/space/comments\" hidefocus=\"on\" rel=\"nofollow\">我的评论</a></dd> <dd><a href=\"//travel.qunar.com/space/msg/sessionlist\" hidefocus=\"on\" rel=\"nofollow\">我的私信</a></dd> </dl> </li> <li class=\"q_header_tnav_omenu\" id=\"__orderInfo_l__\" tabindex=\"-1\"> <dl tabindex=\"-1\"> <dt tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Fuser.qunar.com%2Fflight_toolbox.jsp%3Fcatalog%3Downorders%26from%3Dmyorder&jump=0\" rel=\"nofollow\" class=\"q_header_tnav_omenu_link\" id=\"q_header_tnav_omenu_link\" tabindex=\"0\"><span class=\"q_header_tnav_omenu_title\" id=\"__orderInfo_t__\">查看订单<b id=\"__orderInfo_b__\" class=\"qunar-assist-hide\"></b></span></a></dt> <dd id=\"__orderInfo_w__\" class=\"assist-igrone-tbindex\" tabindex=\"-1\"> <div class=\"q_header_tnav_omenu_item\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fflight%2F%3Ft%3D1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderFlight__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">机票订单</span></a></div> <div class=\"q_header_tnav_omenu_item\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fhotel%2F%3Ffrom%3Dmyorder\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderHotel__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">酒店订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fapartment?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderApartment__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">公寓订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fgroup?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderGroup__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">团购订单</span></a></div> <div class=\"q_header_tnav_omenu_item\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Fdujia.qunar.com%2Fmyorder.jsp%3Ffrom%3Dmyorder\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderVacation__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">度假订单</span></a></div> <div class=\"q_header_tnav_omenu_item\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fticket?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderTicket__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">门票订单</span></a></div> <div class=\"q_header_tnav_omenu_item\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Ftrain?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderTrain__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">火车票订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fbus?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderBus__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">汽车票订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fcar?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderCar__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">车车订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fqmall?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderQmall__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">Q商城订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Flocal?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderDdr__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">当地人订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fpay?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderDdr__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">金融订单</span></a></div> <div class=\"q_header_tnav_omenu_item qunar-assist-hide\" tabindex=\"-1\"><a href=\"//user.qunar.com/order/query.jsp?ret=http%3A%2F%2Forder.qunar.com%2Fpay?t=1\" target=\"_blank\" hidefocus=\"on\" rel=\"nofollow\" data-name=\"__link_orderDdr__\" id=\"__tnav_card_order__\" class=\"q_header_tnav_omenu_m\" tabindex=\"-1\"><span class=\"q_header_tnav_omenu_ordertips\" tabindex=\"-1\">礼品卡订单</span></a></div> </dd> </dl> </li> <li class=\"last\"><a href=\"http://help.qunar.com/\" target=\"_top\" rel=\"nofollow\" id=\"__link_contact__\" tabindex=\"-1\">联系客服</a> </li> </ul> </div> <div class=\"q_header_mnav qunar-assist-hide\"> <ul> <li class=\"qhf_home\"> <a href=\"//www.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_home__\" class=\"q_header_navlink\"><span><b>首页</b></span></a> </li> <li class=\"qhf_flight\"> <a href=\"//flight.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_flight__\" class=\"q_header_navlink\"><span><b>机票</b></span></a> </li> <li class=\"qhf_hotel\"> <a href=\"//hotel.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_hotel__\" class=\"q_header_navlink\"><span><b>酒店</b></span></a> </li> <li class=\"qhf_tuan\"> <a href=\"//tuan.qunar.com/vc/index.php?category=all\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_tuan__\" class=\"q_header_navlink\"><span><b>团购</b></span></a> </li> <li class=\"qhf_package\"> <a href=\"//dujia.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_package__\" class=\"q_header_navlink\"><span><b>度假</b></span></a> </li> <li class=\"qhf_youlun\"> <a href=\"//dujia.qunar.com/pqkd/nalist_%E9%82%AE%E8%BD%AE_liner\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_youlun__\" class=\"q_header_navlink\"><span><b>邮轮</b></span></a> </li> <li class=\"qhf_piao\"> <a href=\"//piao.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_piao__\" class=\"q_header_navlink\"><span><b>门票</b></span></a> </li> <li class=\"qhf_train\"> <a href=\"//train.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_train__\" class=\"q_header_navlink\"><span><b>火车票</b></span></a> </li> <li class=\"qhf_travel\"> <a href=\"//travel.qunar.com/?from=header\" target=\"_top\" hidefocus=\"on\" id=\"__link_travel__\" class=\"q_header_navlink\"><span><b>攻略</b></span></a> </li> <!-- <li class=\"qhf_gongyu\"> <a href=\"http://gongyu.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_gongyu__\" class=\"q_header_navlink\"><span><b>公寓</b></span></a> </li> --> <li class=\"qhf_ddr\"> <a href=\"//i.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_ddr__\" class=\"q_header_navlink q_header_navlink_ddr\"><span><b>当地人</b></span></a> </li> <li class=\"qhf_bus\"> <a href=\"http://bus.qunar.com/\" target=\"_top\" hidefocus=\"on\" id=\"__link_bus__\" class=\"q_header_navlink\"><span><b>汽车票</b></span></a> </li> <!-- <li class=\"qhf_haiwai\"> <a href=\"http://haiwai.qunar.com/\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_haiwai__\" class=\"q_header_navlink\"><span><b>境外</b></span></a> </li> --> <li class=\"qhf_navmore\" id=\"__header_navmore__\"> <a href=\"javascript:void(0)\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" class=\"q_header_navlink q_header_navlink_more\"><span><b>更多</b></span><i></i></a> <div class=\"q_header_navmore-con\" id=\"__header_navmore_con__\"> <!-- <a href=\"http://haiwai.qunar.com/\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_haiwai_navmore__\" class=\"q_header_navlink\"><span><b>境外</b></span></a> --> <!-- <a href=\"http://bao.qunar.com/?from=tierone_nav_pc\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_card_navmore__\" class=\"q_header_navlink\"><span><b>保险</b></span></a> --> <!-- <a href=\"//jr.qunar.com/ious/index.htm\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_jr_navmore__\" class=\"q_header_navlink\"><span><b>金融</b></span></a> --> </div> </li> <!-- <li class=\"qhf_card\"> <a href=\"http://bao.qunar.com/?from=tierone_nav_pc\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_card__\" class=\"q_header_navlink\"><span><b>保险</b></span></a> </li> --> <!-- <li class=\"qhf_jr\"> <a href=\"//jr.qunar.com/ious/index.htm\" rel=\"nofollow\" target=\"_top\" hidefocus=\"on\" id=\"__link_jr__\" class=\"q_header_navlink\"><span><b>金融</b></span></a> </li> --> </ul> <div id=\"__header_nav_tags__\" class=\"q_header_tags\"> <!-- <img src=\"//source.qunarzz.com/common/hf/tags/quanjia.gif\" class=\"qhf_tag_gongyu\"> --> <img src=\"//source.qunarzz.com/common/hf/tags/mp-daytrip.png\" class=\"qhf_tag_piao\"> <!-- <img src=\"//s.qunarzz.com/package/header/summer-promotion.png\" class=\"qhf_tag_package\"> --> <img src=\"//source.qunarzz.com/common/hf/tags/local.gif\" class=\"qhf_tag_local\"> <!-- <img src=\"//source.qunarzz.com/common/hf/tags/insure.gif\" class=\"qhf_tag_card\"> --> <!-- <img src=\"//s.qunarzz.com/m_bus_search/images/bus.png\" class=\"qhf_tag_bjcx\"> --> <!-- <img src=\"//source.qunarzz.com/common/hf/tags/jr_v1.gif\" class=\"qhf_tag_jr\"> --> <!-- <img src=\"//source.qunarzz.com/common/hf/tags/car.gif\" class=\"qhf_tag_car\"> --> <!-- <img src=\"//source.qunarzz.com/common/hf/tags/haiwai.gif\" class=\"qhf_tag_haiwai\"> --> </div> <!--div class=\"q_header_aside\" id=\"__header_aside_flight__\" style=\"left: -35px\"> <a href=\"//flight.qunar.com/?from=header\">机票预定</a> <a href=\"//touch.qunar.com/flight/seat/desktop?from=header\">在线选座</a> <span class=\"q_header_blank\"></span> </div--> <div class=\"q_header_aside\" id=\"__header_aside_package__\" style=\"left:124px;\"> <a href=\"//dujia.qunar.com/?tf=djnavkj_index\" rel=\"nofollow\"><span>度假首页</span></a> <a href=\"//i.qunar.com/?tf=djnavkj_haiwaiwanle\" rel=\"nofollow\"><span>海外玩乐</span></a> <a href=\"//fh.dujia.qunar.com/?tf=package\" rel=\"nofollow\"><span>自由行</span></a> <a href=\"//dujia.qunar.com/p/abroad/?tf=djnavkj_abroad\"><span>出境游</span></a> <a href=\"//dujia.qunar.com/tejia/?tf=djnavkj_tejia\" rel=\"nofollow\"><span>特卖</span></a> <a href=\"//dujia.qunar.com/p/around/?tf=djnavkj_around\"><span>周边游</span></a> <a href=\"//dujia.qunar.com/p/youlun/?tf=djnavkj_youlun\" rel=\"nofollow\"><span>邮轮</span></a> <a href=\"//dujia.qunar.com/visa/?tf=djnavkj_visa\"><span>签证</span></a> <a href=\"//dujia.qunar.com/global/?tf=djnavkj_haiwaigouwu\" rel=\"nofollow\" class=\"qhf_hot\"><span>海外购物</span><i class=\"qhf_hot\"></i></a> <a href=\"//diy.dujia.qunar.com/?tf=djnavkj_diy\" rel=\"nofollow\"><span>包团·定制</span></a> <!-- <a href=\"//dujia.qunar.com/ins/agg/index.qnr?tf=dj_ins\" rel=\"nofollow\"><span>旅行保险</span><i class=\"new\"></i></a> --> <!-- <a href=\"//dujia.qunar.com/p/domestic/?tf=djnavkj_domestic\"><span>国内游</span></a> --> <!-- <a href=\"//dujia.qunar.com/p/tuan/?tf=djnavkj_tuan\"><span>团购</span></a> --> <span class=\"q_header_blank\"></span> </div> <div class=\"q_header_aside\" id=\"__header_aside_travel__\" style=\"left:353px;\"> <a href=\"http://travel.qunar.com/?from=header\"><span>攻略首页</span></a> <a href=\"http://travel.qunar.com/travelbook/list.htm?order=hot_heat\"><span>攻略库</span></a> <a href=\"http://travel.qunar.com/place/?from=header\"><span>目的地</span></a> <!--<a href=\"http://travel.qunar.com/bbs/?from=header\"><span>论坛</span></a>--> <a href=\"http://travel.qunar.com/plan/first?from=header\" rel=\"nofollow\"><span>创建行程</span></a> <a href=\"http://travel.qunar.com/youji/create?from=header\" rel=\"nofollow\" class=\"qhf_hot\"><span>发表游记</span><i class=\"qhf_hot\"></i></a> <!-- <a href=\"http://guide.qunar.com/\" rel=\"nofollow\"><span>骆驼书</span></a> --> <a href=\"//travel.qunar.com/travelbook/creator/apply?source=qunar_first\"><span>创作者平台</span></a> <span class=\"q_header_blank\"></span> </div> <div class=\"q_header_aside\" id=\"__header_aside_piao__\" style=\"left:230px;\"> <a href=\"http://piao.qunar.com/\"><span>境内门票</span></a> <a href=\"http://piao.qunar.com/daytrip/list.htm\" class=\"qhf_hot\"><span>品质一日游</span><i class=\"qhf_hot\"></i></a> <!-- <a href=\"http://piao.qunar.com/overseas/\"><span>玩转海外</span></a> --> <!-- <a href=\"http://piao.qunar.com/topic/gat1409.htm\"><span>港澳台门票</span></a> --> <a href=\"http://piao.qunar.com/ticket/vista.htm\" rel=\"nofollow\"><span>地图找景点</span></a> <span class=\"q_header_blank\"></span> </div> <div class=\"q_header_aside\" id=\"__header_aside_tuan__\" style=\"left:71px\"> <a href=\"//tuan.qunar.com/vc/index.php?category=all\" rel=\"nofollow\"><span>度假团购</span></a> <!-- <a href=\"http://tuan.qunar.com/#PAGE/L2JzbmVzL3R1YW5XZWIvbGlzdA__/category/aG90ZWw_\"><span>酒店团购</span></a> --> <a href=\"//tuan.qunar.com/vc/index.php?category=around\" rel=\"nofollow\"><span>周边休闲</span></a> <a href=\"//tuan.qunar.com/vc/index.php?category=travel_d\" rel=\"nofollow\"><span>长线游</span></a> <span class=\"q_header_blank\"></span> </div> <!-- <div class=\"q_header_aside\" id=\"__header_aside_jr__\" style=\"left:750px\"> <a href=\"//jr.qunar.com/ious/index.htm\" rel=\"nofollow\"><span>拿去花</span></a> <a href=\"http://bao.qunar.com/?from=qunar_nav_pc\" rel=\"nofollow\"><span>保险商城</span></a> <span class=\"q_header_blank\" style=\"left: 54px;\"></span> </div> --> <div class=\"q_header_aside\" id=\"__header_aside_ddr__\" style=\"left:406px\"> <a href=\"http://i.qunar.com?tf=ejsy\" rel=\"nofollow\"><span>当地人首页</span></a> <a href=\"http://i.qunar.com/web/search/index?q=%E5%87%BA%E5%A2%83WiFi&tf=ejcjwf\" rel=\"nofollow\" class=\"qhf_hot\"><span>出境WiFi</span><i class=\"qhf_hot\"></i></a> <a href=\"http://i.qunar.com/web/search/index?q=%E6%97%85%E6%B8%B8%E5%8C%85%E8%BD%A6&tf=ejlybc\" rel=\"nofollow\"><span>旅游包车</span></a> <a href=\"http://i.qunar.com/web/search/index?tm=i02&q=%E4%B8%80%E6%97%A5%E6%B8%B8&tf=ejyry\" rel=\"nofollow\"><span>一日游</span></a> <a href=\"http://i.qunar.com/web/search/index?q=%E5%AF%BC%E6%B8%B8%E8%AE%B2%E8%A7%A3&tf=ejdyjj\" rel=\"nofollow\"><span>导游讲解</span></a> <a href=\"http://i.qunar.com/web/search/index?q=%E4%BA%A4%E9%80%9A%E5%8D%A1&tf=ejjtk\" rel=\"nofollow\"><span>交通卡</span></a> <span class=\"q_header_blank q_header_blank_ddr\"></span> </div> <div class=\"q_header_aside\" id=\"__header_aside_car__\" style=\"left:476px\"> <a href=\"//zuche.qunar.com/index#channelID=14456\" rel=\"nofollow\" class=\"qhf_hot\"><span>国内租车</span><i class=\"new\"></i></a> <a href=\"http://car.ctrip.com/hwzijia?channelid=16033&allianceid=811740&sid=1373418\" rel=\"nofollow\" class=\"qhf_hot\"><span>境外租车</span><i class=\"qhf_hot\"></i></a> <a href=\"http://haiwai.qunar.com/web/och/index?channelid=14314\" rel=\"nofollow\"><span>国际接送机</span></a> <span class=\"q_header_blank\"></span> </div> </div> <a class=\"q_header_app_logo\" href=\"http://app.qunar.com/\" rel=\"nofollow\" tabindex=\"-1\"></a>\\n</div>\\n<script type=\"text/javascript\" src=\"//s.qunarzz.com/common/assist/************/qunar-assist.js\"></script> <script id=\"hf-script\" src=\"//common.qunarzz.com/hf_qzz/prd/scripts/default/<EMAIL>\"></script> <!--[if lte IE 8]> <link data-hfstamp=\"20250116162035\" id=\"static-style\" data-hffile=\"header_main_home\" rel=\"stylesheet\" href=\"//common.qunarzz.com/static/ie_guide/prd/styles/index.min.css\"> <script id=\"static-script\" src=\"//common.qunarzz.com/static/ie_guide/prd/scripts/index.min.js\"></script> <![endif]--> <script id=\"header-script\" src=\"//common.qunarzz.com/static/header/prd/scripts/index.min.js\"></script>\\n<script id=\"hf-script-exec\" type=\"text/javascript\">\\n(function() { QNR.TAB = \"travel\"; var userstatus = QNR.QUstatus; userstatus.run({ \\'u\\' : \\'__loginInfo_u__\\', \\'t\\' : \\'__loginInfo_t__\\', \\'l\\' : \\'__loginInfo_l__\\', \\'r\\' : \\'__loginInfo_r__\\' });\\n})();\\nQNR.hfUtils.setNav(\\'travel\\')\\n</script>\\n<div class=\"q_header_sub\"> <div class=\"q_header_sub_inner\"> <div class=\"q_header_sub_menu_r\"> <div class=\"tag_hd hide\"> <div class=\"sum\" style=\"display:none\">0</div> <a href=\"//user.qunar.com/passport/login.jsp?ret=http%3A%2F%2Ftravel.qunar.com%2F\" hidefocus=\"on\" rel=\"nofollow\"><img class=\"img\" width=\"30\" height=\"30\" src=\"//s.qunarzz.com/travel/common/headshot.png\"></a> <b class=\"arrow\"></b> </div> <div class=\"tag_ct hide\"> <em class=\"tag_top\"></em> <ul class=\"tag_box\"></ul> </div> </div> <div class=\"general_search\"> <div name=\"travel_common_top\"> <div class=\"bg\"> <input maxlength=\"40\" type=\"text\" data-default=\"搜目的地/攻略\" value=\"搜目的地/攻略\"><a class=\"btn\" href=\"#\" rel=\"nofollow\" data-beacon=\"search_header\"></a> </div> </div> <div class=\"search_his\"> <dl class=\"hot\"> <dt class=\"tit\">热门搜索</dt> </dl> <dl class=\"history\"> <dt class=\"tit\"><a class=\"cl_his\" href=\"#\" rel=\"nofollow\">清除</a>历史记录</dt> </dl> </div> </div> <ul class=\"q_header_sub_menu_l\"> <li data-headerclk=\"hd_main_navigation_home\" class=\"home\"><a hidefocus=\"on\" href=\"//travel.qunar.com/\">攻略首页</a></li> <li data-headerclk=\"hd_main_navigation_travelbook\" class=\"travelbook\"><a hidefocus=\"on\" href=\"//travel.qunar.com/travelbook/list.htm?order=hot_heat\" rel=\"nofollow\">攻略库</a></li> <li data-headerclk=\"hd_main_navigation_place\" class=\"place\"><a hidefocus=\"on\" href=\"//travel.qunar.com/place/\">目的地</a></li> <!--<li data-headerclk=\"hd_main_navigation_bbs\" class=\"bbs\"><a hidefocus=\"on\" href=\"http://travel.qunar.com/bbs/\">论坛</a></li>--> <li data-headerclk=\"hd_main_navigation_top_right_bookcreate\" class=\"plan\"><a hidefocus=\"on\" target=\"_blank\" href=\"//travel.qunar.com/plan/first/\" rel=\"nofollow\"><b class=\"icon_create\"></b>创建行程</a></li> <li data-headerclk=\"hd_main_navigation_top_right_notescreate\" class=\"notes\"><a hidefocus=\"on\" class=\"link\" target=\"_blank\" href=\"//travel.qunar.com/youji/create\" rel=\"nofollow\"><b class=\"icon_create\"></b>发表游记</a><a href=\"//www.qunar.com/site/zh/mall.shtml?bdsource=pc\" target=\"_blank\" class=\"notes_gif\" data-beacon=\"top_right_notescreate_jifen\"><img src=\"//s.qunarzz.com/travel/place/Q_score_jifen.gif\" alt=\"\"/></a></li> <li data-headerclk=\"hd_main_navigation_creator\" class=\"creator\"><a hidefocus=\"on\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/creator/home\">创作者平台</a></li> </ul> </div> </div> <script> window.HEADER_VER = 20250116162035; </script> <script src=\"//common.qunarzz.com/travel_new_plan/prd/scripts/travel/search.js?t=20250116162035\"></script> </div>\\n\\t<div class=\"q_skin\">\\n\\t<div class=\"container\">\\n\\t\\t<div class=\"b_errmsg\">\\n\\t\\t\\t非常抱歉，您访问的页面不存在，可能已被删除或您输错了网址<br><a class=\"go_back\" href=\"#\">返回上一页</a>\\n        </div>\\n\\n            <div class=\"b_tag_index b_tag_index_finishpage\">\\n\\t\\t                <h2 class=\"tit\">攻略分类导航</h2>\\n\\t\\t                <dl class=\"types\">\\n\\t\\t                    <dt>月&nbsp;&nbsp;&nbsp;份：</dt>\\n\\t\\t                    <dd>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_month\" href=\"//travel.qunar.com/travelbook/list/default---1_2_3--1/q\">1-3月</a>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_month\" href=\"//travel.qunar.com/travelbook/list/default---4_5_6--1/q\">4-6月</a>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_month\" href=\"//travel.qunar.com/travelbook/list/default---7_8_9--1/q\">7-9月</a>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_month\" class=\"last\" href=\"//travel.qunar.com/travelbook/list/default---10_11_12--1/q\">10-12月</a>\\n\\t\\t                    </dd>\\n\\t\\t                </dl>\\n\\n\\t\\t                <dl class=\"types\">\\n\\t\\t                    <dt>主&nbsp;&nbsp;&nbsp;题：</dt>\\n\\t\\t                    <dd>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_TAG\" href=\"//travel.qunar.com/travelbook/list/default--%E5%8F%A4%E9%95%87%E6%97%B6%E5%85%89---1/q\">古镇时光</a>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_TAG\" href=\"//travel.qunar.com/travelbook/list/default--%E9%9D%A2%E6%9C%9D%E5%A4%A7%E6%B5%B7---1/q\">面朝大海</a>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_TAG\" href=\"//travel.qunar.com/travelbook/list/default--%E5%90%83%E8%B4%A7%E8%A1%80%E6%8B%BC---1/q\">吃货血拼</a>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_TAG\" href=\"//travel.qunar.com/travelbook/list/default--%E6%88%B7%E5%A4%96%E6%92%92%E9%87%8E---1/q\">户外撒野</a>\\n\\t\\t                        <a target=\"_blank\" data-beacon=\"click_guild_TAG\" class=\"last\" href=\"//travel.qunar.com/travelbook/list/default--%E6%83%85%E8%BF%B7%E8%87%AA%E9%A9%BE---1/q\">情迷自驾</a>\\n\\t\\t                    </dd>\\n\\t\\t                </dl>\\n\\n\\t\\t                <dl class=\"types\">\\n\\t\\t                    <dt>目的地：</dt>\\n\\t\\t                        <dd>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%258E%25A6%25E9%2597%25A8\">厦门</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%2588%2590%25E9%2583%25BD\">成都</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25B3%25B0%25E5%259B%25BD\">泰国</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25B3%2595%25E5%259B%25BD\">法国</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25BE%25B3%25E5%25A4%25A7%25E5%2588%25A9%25E4%25BA%259A\">澳大利亚</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E4%25B8%25BD%25E6%25B1%259F\">丽江</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E4%25B8%2589%25E4%25BA%259A\">三亚</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E9%25A6%2599%25E6%25B8%25AF\">香港</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%259D%25AD%25E5%25B7%259E\">杭州</a>\\n\\t\\t                        <a class=\"kwd\" data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E8%25A5%25BF%25E5%25AE%2589\">西安</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%258C%2597%25E4%25BA%25AC\">北京</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E9%2587%258D%25E5%25BA%2586\">重庆</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25A1%2582%25E6%259E%2597\">桂林</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E4%25B8%258A%25E6%25B5%25B7\">上海</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E9%25BB%2584%25E5%25B1%25B1\">黄山</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%25A4%25A9%25E6%25B4%25A5\">天津</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%2598%2586%25E6%2598%258E\">昆明</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%25B9%25BF%25E5%25B7%259E\">广州</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%258D%2597%25E4%25BA%25AC\">南京</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25B7%25B1%25E5%259C%25B3\">深圳</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E8%258B%258F%25E5%25B7%259E\">苏州</a>\\n\\t\\t                        <a class=\"kwd\" data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%2597%25A0%25E9%2594%25A1\">无锡</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25B5%25B7%25E5%258F%25A3\">海口</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E7%25A7%25A6%25E7%259A%2587%25E5%25B2%259B\">秦皇岛</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25AD%25A6%25E6%25B1%2589\">武汉</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%2589%25AC%25E5%25B7%259E\">扬州</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%2583%25A0%25E5%25B7%259E\">惠州</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%25BC%25A0%25E5%25AE%25B6%25E7%2595%258C\">张家界</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%2593%2588%25E5%25B0%2594%25E6%25BB%25A8\">哈尔滨</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%25A4%25A7%25E5%2590%258C\">大同</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E9%259D%2592%25E5%25B2%259B\">青岛</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E4%25B9%2590%25E5%25B1%25B1\">乐山</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E6%25B9%2598%25E8%25A5%25BF\">湘西</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E9%2598%25BF%25E5%259D%259D\">阿坝</a>\\n\\t\\t                        <a data-beacon=\"click_guild_des\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/list/default-----1/q%25E5%25A4%25AA%25E5%258E%259F\">太原</a>\\n\\t\\t                        <a class=\"more\" target=\"_blank\" data-beacon=\"click_guild_all\" href=\"//travel.qunar.com/travelbook/guide/nav\">更多</a>\\n\\t\\t                    </dd>\\n\\t\\t                </dl>\\n\\t\\t</div>\\n\\t</div>\\n\\t\\n</div>\\n        \\t</div>\\n        \\t\\n        \\t<div class=\"qn_footer\">\\n        \\t\\t<div class=\"inner\">\\n        \\t\\t\\t\\t\\t<style type=\"text/css\">\\r\\n\\t\\t\\t.qn_footer .copyright {padding:7px 0 0;line-height:22px;margin: 0;}\\r\\n\\t\\t\\t.qn_footer .copyright span,.qn_footer .copyright a,.qn_footer .copyright a:visited{margin-right:7px;padding:2px 0;color:#999;text-decoration: none;}\\r\\n\\t\\t\\t.qn_footer .copyright .s24{background:url(//source.qunarzz.com/common/hf/24h.png) no-repeat left center;padding-left:20px;}\\r\\n\\r\\n\\t\\t\\t.qn_footer .fh-about{display: inline-block;*zoom:1;*display:inline;width:110px;text-align:left;border:1px solid transparent;_border:0;_padding:1px;white-space:nowrap;}\\r\\n\\t\\t\\t.qn_footer .fh-about a{padding-left:6px;}\\r\\n\\t\\t\\t.qn_footer .fh-about .about-link{margin:0;*zoom:1;}\\r\\n\\t\\t\\t.qn_footer .fh-about.hover{border:1px solid #ddd;padding:0;z-index: 999;background: #fff;}\\r\\n\\t\\t\\t.qn_footer .fh-about.hover .about-link{color:#ff9e5e;margin:0;}\\r\\n\\t\\t\\t.qn_footer .fh-about.hover .fh-list{display: block !important;}\\r\\n\\t\\t\\t.qn_footer .fh-about .fh-list{position: absolute;left:-1px;bottom:22px;border:1px solid #ddd;background: #fff;width:110px;display:none;white-space:normal;}\\r\\n\\t\\t\\t.qn_footer .fh-about .fh-list a{display: block;margin:0;*margin:0 !important;*width: 70%;}\\r\\n\\t\\t\\t.qn_footer .fh-about .fh-list a:hover{color:#666;background: #e2e2e2;*background:#fff;*color:#ff9e5e;}\\r\\n\\t\\t\\t.qn_footer .fh-about i{font-size:0;background:url(//source.qunarzz.com/common/hf/arc.png) no-repeat left bottom;width:7px;height:4px;display: inline-block;*display:inline;*zoom:1;margin:0 0 2px 3px;}\\r\\n\\t\\t\\t.qn_footer .fh-about.hover i{background-position:0 0;}\\r\\n\\t\\t\\t.qn_footer .fh-list-wrapper{height:0;position:relative;z-index: 999;*float: left;}\\r\\n\\r\\n\\t\\t\\t.qn_footer { clear: both; margin: 0 auto; padding: 15px 0 25px; width: 979px; line-height: 1.231; font-family: Arial, Helvetica, sans-serif; font-size: 12px; color: #848484;}\\r\\n\\t\\t\\t.qn_footer .inner { text-align: right; }\\r\\n\\t\\t\\t.qn_footer .gs { float: right; margin: 0 20px 0 0; }\\r\\n\\t\\t\\t.qn_footer .cr { margin-right: 0; padding-bottom: 11px;text-align:center;}\\r\\n\\t\\t\\t.qn_footer .links { margin: 0; padding: 2px 0 0; list-style: none;color:#999;display:block; }\\r\\n\\t\\t\\t.qn_footer .links span{*display:inline;*zoom:1;}\\r\\n\\t\\t\\t.qn_footer .links a,\\r\\n\\t\\t\\t.qn_footer .links a:visited { padding: 0 5px;margin:0; text-decoration: none; color: #999;display:inline-block;*zoom:1;*display:inline;height:22px;line-height: 22px;white-space:nowrap; }\\r\\n\\t\\t\\t.qn_footer .links a:hover { color: #f60; }\\r\\n\\t\\t\\t.qn_footer .links a.hl,\\r\\n\\t\\t\\t.qn_footer .links a.hl:visited { color: #ff5555; }\\r\\n\\t\\t\\t.qn_footer .clr{clear:both;height:0;overflow:hidden;}\\r\\n\\r\\n\\t\\t\\t.qn_footer .f_imglist{margin-bottom:10px;float:left;position: relative;left: 50%;}\\r\\n\\t\\t\\t.qn_footer .f_imglist ul{margin:0 auto;padding-bottom:4px;overflow:hidden;position: relative;left: -50%;}\\r\\n\\t\\t\\t.qn_footer .f_imglist li{float:left;margin-right:10px;display:inline;}\\r\\n\\t\\t\\t.qn_footer .f_imglist li a{display:block;width:100%;height:30px;}\\r\\n\\t\\t\\t.qn_footer .f_imglist li a span,.qn_footer .f_imglist li.f_icon_pata span{position:relative;z-index:-1;color:#0086A0;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_cnnic{width:88px;height:30px;background:url(//source.qunarzz.com/common/hf/footer_v10.png) -431px 0 no-repeat;margin-right:0;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_ec{width:87px;height:30px;background:url(//source.qunarzz.com/common/hf/footer_v10.png) -339px 0 no-repeat;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_beian{width:78px;height:30px;background:url(//source.qunarzz.com/common/hf/footer_v10.png) -256px 0 no-repeat;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_itrust{width:91px;height:30px;background:url(//source.qunarzz.com/common/hf/footer_v10.png) -160px 0 no-repeat;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_pci{width:49px;height:30px;background:url(//source.qunarzz.com/common/hf/footer_v10.png) -106px 0 no-repeat;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_verisign{width:101px;height:30px;background:url(//source.qunarzz.com/common/hf/footer_v10.png) 0 0 no-repeat;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_jubaocenter{width:87px;height:30px;background:url(//picbed.qunarzz.com/b7a21dd65d899f5ef3e81abee2ef8842.png) -523px 0 no-repeat;}\\r\\n\\t\\t\\t.qn_footer .f_imglist .f_icon_cntrust{width:97px;height:30px;background:url(//picbed.qunarzz.com/b7a21dd65d899f5ef3e81abee2ef8842.png) -613px 0 no-repeat;}\\r\\n\\r\\n\\t\\t\\t.seo { border-top: 1px solid #ddd; }\\r\\n\\t\\t\\t.seo .seo_links { clear: both; margin: 10px 0 0; padding: 0; color: #a7a7a7; }\\r\\n\\t\\t\\t.seo .seo_links a,\\r\\n\\t\\t\\t.seo .seo_links a:visited { margin-right: 6px; text-decoration: none; color: #a7a7a7; }\\r\\n\\t\\t\\t.seo .seo_links dt { float: left; padding-left: 25px; font-weight: 700; }\\r\\n\\t\\t\\t.seo .seo_links dd { margin-left: 88px; padding: 0; word-spacing: 5px; }\\r\\n\\t\\t\\t.seo .seo_links dd span { float: left; margin-right: 6px; white-space: nowrap; }\\r\\n\\t\\t\\t.seo .seo_links dd .clr { clear: both; height: 0; overflow: hidden; }\\r\\n\\t\\t</style>\\r\\n\\t\\t<div class=\"cr\">\\r\\n\\t\\t\\t<div class=\"links\">\\r\\n\\t\\t\\t\\t<div class=\"fh-about\" onmouseover=\"this.className+=\\' hover\\'\" onmouseout=\"this.className=this.className.replace(/\\\\shover/g,\\'\\')\"><a href=\"http://www.qunar.com/site/zh/Qunar.in.China_1.2.shtml\" target=\"_blank\" rel=\"nofollow\" class=\"about-link\">关于Qunar.com</a><i></i><div class=\"fh-list-wrapper\"><div class=\"fh-list\"> <a href=\"http://www.qunar.com/site/zh/ContactUs_3.shtml\" target=\"_blank\" rel=\"nofollow\">联系我们</a>  <a href=\"http://www.qunar.com/site/zh/Rules.shtml\" target=\"_blank\" rel=\"nofollow\">用户协议</a> <a href=\"http://www.qunar.com/site/zh/Question_7.shtml\" target=\"_blank\" rel=\"nofollow\">常见问题</a><a href=\"http://www.qunar.com/site/zh/Links_8.shtml\" target=\"_blank\" rel=\"nofollow\">友情链接</a></div></div></div><span>|</span><a href=\"http://www.qunar.com/site/zh/Cooperate_4.shtml\" target=\"_blank\" rel=\"nofollow\">业务合作</a><span>|</span><a href=\"//app.mokahr.com/apply/qunar/4206\" target=\"_blank\" rel=\"nofollow\">加入我们</a><span>|</span><a href=\"http://help.qunar.com/complaint.html\" class=\"\" target=\"_blank\" rel=\"nofollow\">\"严重违规失信\"专项整治举报</a><span>|</span><a href=\"http://security.qunar.com\"   target=\"_blank\" rel=\"nofollow\">安全中心</a><span>|</span><a href=\"http://www.qunar.com/commonweal/index.html\" rel=\"nofollow\" target=\"_blank\" >星骆驼公益</a><span>|</span><a href=\"http://www.qunar.com/site/en/Qunar.in.China_1.1.shtml\" target=\"_blank\" rel=\"nofollow\">About Us</a><span>|</span><a href=\"https://group.trip.com/\" target=\"_blank\" rel=\"nofollow\">Trip.com Group</a>\\r\\n\\t\\t\\t</div>\\r\\n\\t\\t\\t<p class=\"copyright\"><span>Copyright &copy;2021 Qunar.com</span><a href=\"http://www.beian.gov.cn/portal/recordQuery?token=9851cd51-44d0-4a7d-9b37-c5c74a9236da\" target=\"_blank\" rel=\"nofollow\">京公网安备11010802030542</a><a href=\"https://beian.miit.gov.cn/\" target=\"_blank\" rel=\"nofollow\">京ICP备05021087号</a><a href=\"http://www.qunar.com/site/company_icp.htm\" target=\"_blank\" rel=\"nofollow\">京ICP证060856号</a><a href=\"http://www.qunar.com/site/company_info.htm\" target=\"_blank\" rel=\"nofollow\">营业执照信息</a><a href=\"http://www.qunar.com/site/company_drug_info.htm\" target=\"_blank\" rel=\"nofollow\">互联网药品信息服务资格证：(京)-非经营性-2016-0110</a><span class=\"s24\">去哪儿网投诉、咨询热线电话95117</span><span>举报、投诉邮箱: <EMAIL></span><span>全国旅游投诉热线: 12345</span></p>\\r\\n\\t\\t\\t<p class=\"copyright\"><span>未成年人/违法和不良信息/算法推荐举报电话：010-59606977</span><span>未成年人/违法和不良信息/算法推荐举报邮箱：<EMAIL></span></p>\\r\\n\\t\\t</div>\\r\\n\\t\\t<div class=\"f_imglist\">\\r\\n\\t\\t\\t<ul>\\r\\n\\t\\t\\t\\t<li><img src=\"//s.qunarzz.com/f_cms/2022/1642594269680_633725513.png\" height=\"30\" /></li>\\r\\n\\t\\t\\t\\t<li><a href=\"//cn.globalsign.com/\" target=\"_blank\" rel=\"nofollow\"><img src=\"//s.qunarzz.com/hf/approve/globalsign.gif\" height=\"30\" /></a></li>\\r\\n\\t\\t\\t\\t<li class=\"f_icon_pci\"><a href=\"http://qimgs.qunarzz.com/wpf_newmpic_001/0fcbeeff812c27178978f893554b9d9a.pdf\" target=\"_blank\" rel=\"nofollow\" title=\"去哪儿网通过PCI认证\"></a></li>\\r\\n\\t\\t\\t\\t<!-- <li class=\"f_icon_itrust\"><a href=\"http://www.itrust.org.cn/yz/pjwx.asp?wm=1786892425\" target=\"_blank\" rel=\"nofollow\" title=\"信用编码：1786892425\"></a></li>\\r\\n\\t\\t\\t\\t<li class=\"f_icon_beian\"><a href=\"http://www.hd315.gov.cn/beian/view.asp?bianhao=010202007112700003\" target=\"_blank\" rel=\"nofollow\" title=\"经营性网站备案信息\"></a></li>\\r\\n\\t\\t\\t\\t<li class=\"f_icon_cnnic\"><a href=\"//ss.knet.cn/verifyseal.dll?sn=e131022110100429697dhp000000&ct=df&a=1&pa=500267\" target=\"_blank\" rel=\"nofollow\" title=\"可信网站身份验证\"></a></li> -->\\r\\n\\t\\t\\t\\t<li class=\"f_icon_jubaocenter\"><a href=\"http://www.12377.cn/\" target=\"_blank\" rel=\"nofollow\" title=\"违法和不良信息举报中心\"></a></li>\\r\\n\\t\\t\\t\\t<li class=\"f_icon_cntrust\"><a href=\"https://www.creditchina.gov.cn/\" target=\"_blank\" rel=\"nofollow\" title=\"信用中国\"></a></li>\\r\\n\\t\\t\\t</ul>\\r\\n\\t\\t\\t<div class=\"clr\"></div>\\r\\n\\t\\t</div>\\r\\n\\t\\t<script> (function(){var i = new Image(); i.src = \"//user.qunar.com/passport/addICK.jsp\" + ( document.location.protocol === \"https:\" ? \"?ssl\" : \"\"); })(); </script>\\r\\n</div>\\n        \\t</div>\\n\\t<!-- \\n\\t<div data-headerclk=\"hd_main_navigation_mobile\" class=\"q_quick_mark\">\\n    <a class=\"btn_close js_close\" title=\"关闭\" href=\"#\"></a>\\n    <a class=\"img_quickmark\" hidefocus=\"on\" target=\"_blank\" href=\"//travel.qunar.com/travelbook/phone/?from=traveltop\">手机版</a>\\n\\t</div>\\n\\t -->\\n        <script src=\"//q.qunarzz.com/jquery/prd/jquery-1.7.2.min.js\"></script>\\n        \\t<script>var PRE_SID = \"354\" ;</script>\\n <script src=\"//q.qunarzz.com/travel_new_plan/prd/scripts/travel/<EMAIL>\"></script></body></html>\\n'"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "\"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0\"\n", "\"Mozilla/5.0（Windows NT 10.0；Win64；x64；rv:124.0）Gecko/20100101 Firefox/124.0\"\n", "\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/123.0.2420.81\"\n", "\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********\"\n", "\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "Mozilla/5.0 (Macintosh; Intel Mac OS X 14.4; rv:124.0) Gecko/20100101 Firefox/124.0\n", "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15\n", "Mozilla/5.0 (Macintosh; Intel Mac OS X 14_4_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********\n", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n", "Mozilla/5.0 (X11; Linux i686; rv:124.0) Gecko/20100101 Firefox/124.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["pandas 查询 sqlite 数据库\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "DatabaseError", "evalue": "Execution failed on sql 'SELECT * FROM proxy_pool': no such table: proxy_pool", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mOperationalError\u001b[0m                          <PERSON><PERSON> (most recent call last)", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\pandas\\io\\sql.py:2674\u001b[0m, in \u001b[0;36mSQLiteDatabase.execute\u001b[1;34m(self, sql, params)\u001b[0m\n\u001b[0;32m   2673\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 2674\u001b[0m     \u001b[43mcur\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2675\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cur\n", "\u001b[1;31mOperationalError\u001b[0m: no such table: proxy_pool", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mDatabaseError\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[1;32mIn[1], line 8\u001b[0m\n\u001b[0;32m      5\u001b[0m connection \u001b[38;5;241m=\u001b[39m sqlite3\u001b[38;5;241m.\u001b[39mconnect(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mproxy_pool.db\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      7\u001b[0m \u001b[38;5;66;03m# 使用pandas读取数据\u001b[39;00m\n\u001b[1;32m----> 8\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_sql_query\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mSELECT * FROM proxy_pool\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconnection\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     10\u001b[0m \u001b[38;5;66;03m# 显示数据\u001b[39;00m\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28mprint\u001b[39m(df)\n", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\pandas\\io\\sql.py:526\u001b[0m, in \u001b[0;36mread_sql_query\u001b[1;34m(sql, con, index_col, coerce_float, params, parse_dates, chunksize, dtype, dtype_backend)\u001b[0m\n\u001b[0;32m    523\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m dtype_backend \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m lib\u001b[38;5;241m.\u001b[39mno_default\n\u001b[0;32m    525\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m pandasSQL_builder(con) \u001b[38;5;28;01mas\u001b[39;00m pandas_sql:\n\u001b[1;32m--> 526\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mpandas_sql\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_query\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    527\u001b[0m \u001b[43m        \u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    528\u001b[0m \u001b[43m        \u001b[49m\u001b[43mindex_col\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindex_col\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    529\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    530\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcoerce_float\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcoerce_float\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    531\u001b[0m \u001b[43m        \u001b[49m\u001b[43mparse_dates\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparse_dates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    532\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunksize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunksize\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    533\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    534\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdtype_backend\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype_backend\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    535\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\pandas\\io\\sql.py:2738\u001b[0m, in \u001b[0;36mSQLiteDatabase.read_query\u001b[1;34m(self, sql, index_col, coerce_float, parse_dates, params, chunksize, dtype, dtype_backend)\u001b[0m\n\u001b[0;32m   2727\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mread_query\u001b[39m(\n\u001b[0;32m   2728\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   2729\u001b[0m     sql,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   2736\u001b[0m     dtype_backend: DtypeBackend \u001b[38;5;241m|\u001b[39m Literal[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnumpy\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnumpy\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   2737\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m DataFrame \u001b[38;5;241m|\u001b[39m Iterator[DataFrame]:\n\u001b[1;32m-> 2738\u001b[0m     cursor \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43msql\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2739\u001b[0m     columns \u001b[38;5;241m=\u001b[39m [col_desc[\u001b[38;5;241m0\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m col_desc \u001b[38;5;129;01min\u001b[39;00m cursor\u001b[38;5;241m.\u001b[39mdescription]\n\u001b[0;32m   2741\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\pandas\\io\\sql.py:2686\u001b[0m, in \u001b[0;36mSQLiteDatabase.execute\u001b[1;34m(self, sql, params)\u001b[0m\n\u001b[0;32m   2683\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ex \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01minner_exc\u001b[39;00m\n\u001b[0;32m   2685\u001b[0m ex \u001b[38;5;241m=\u001b[39m DatabaseError(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExecution failed on sql \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msql\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mexc\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m-> 2686\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m ex \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mexc\u001b[39;00m\n", "\u001b[1;31mDatabaseError\u001b[0m: Execution failed on sql 'SELECT * FROM proxy_pool': no such table: proxy_pool"]}], "source": ["import sqlite3\n", "import pandas as pd\n", "\n", "# 连接到SQLite数据库\n", "connection = sqlite3.connect('proxy_pool.db')\n", "\n", "# 使用pandas读取数据\n", "df = pd.read_sql_query(\"SELECT * FROM proxy_pool\", connection)\n", "\n", "# 显示数据\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[b'use_proxy']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 引入Redis库\n", "import redis\n", "# 连接运行的redis服务，host为Redis服务的ip地址，也可以是url链接\n", "r = redis.StrictRedis(host=\"127.0.0.1\", port=6379, db=0)\n", "\n", "keys = r.keys()\n", "keys\n", "\n", "# for key in redis_client.scan_iter():\n", "#     print(f\"Key: {key}, value: {redis_client.get(key)}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"ename": "ResponseError", "evalue": "WRONGTYPE Operation against a key holding the wrong kind of value", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mResponseError\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 4\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 遍历键并获取对应的值\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m key \u001b[38;5;129;01min\u001b[39;00m keys:\n\u001b[0;32m      3\u001b[0m     \u001b[38;5;66;03m# 根据数据类型获取值（示例以字符串类型为例）\u001b[39;00m\n\u001b[1;32m----> 4\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[43mr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      5\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mKey: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkey\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, Value: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mvalue\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\redis\\client.py:1606\u001b[0m, in \u001b[0;36mRedis.get\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1602\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mget\u001b[39m(\u001b[38;5;28mself\u001b[39m, name):\n\u001b[0;32m   1603\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m   1604\u001b[0m \u001b[38;5;124;03m    Return the value at key ``name``, or None if the key doesn't exist\u001b[39;00m\n\u001b[0;32m   1605\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m-> 1606\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute_command\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mGET\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\redis\\client.py:901\u001b[0m, in \u001b[0;36mRedis.execute_command\u001b[1;34m(self, *args, **options)\u001b[0m\n\u001b[0;32m    899\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m    900\u001b[0m     conn\u001b[38;5;241m.\u001b[39msend_command(\u001b[38;5;241m*\u001b[39margs)\n\u001b[1;32m--> 901\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparse_response(conn, command_name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions)\n\u001b[0;32m    902\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mConnectionError\u001b[39;00m, \u001b[38;5;167;01mTimeoutError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    903\u001b[0m     conn\u001b[38;5;241m.\u001b[39mdisconnect()\n", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\redis\\client.py:915\u001b[0m, in \u001b[0;36mRedis.parse_response\u001b[1;34m(self, connection, command_name, **options)\u001b[0m\n\u001b[0;32m    913\u001b[0m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mParses a response from the Redis server\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    914\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 915\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_response\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    916\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ResponseError:\n\u001b[0;32m    917\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m EMPTY_RESPONSE \u001b[38;5;129;01min\u001b[39;00m options:\n", "File \u001b[1;32md:\\Program\\anaconda3\\envs\\webcrawl_py310\\lib\\site-packages\\redis\\connection.py:756\u001b[0m, in \u001b[0;36mConnection.read_response\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    753\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnext_health_check \u001b[38;5;241m=\u001b[39m time() \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhealth_check_interval\n\u001b[0;32m    755\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response, ResponseError):\n\u001b[1;32m--> 756\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m response\n\u001b[0;32m    757\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[1;31mResponseError\u001b[0m: WRONGTYPE Operation against a key holding the wrong kind of value"]}], "source": ["# 遍历键并获取对应的值\n", "for key in keys:\n", "    # 根据数据类型获取值（示例以字符串类型为例）\n", "    value = r.get(key)\n", "    print(f\"Key: {key}, Value: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[b'use_proxy']\n", "use_proxy\n", "键: use_proxy, 类型: hash\n", "  哈希表内容:\n", "    **********:3128: {\"proxy\": \"**********:3128\", \"https\": true, \"fail_count\": 0, \"region\": \"error\", \"anonymous\": \"\", \"source\": \"freeProxy11\", \"check_count\": 3, \"last_status\": true, \"last_time\": \"2025-04-12 20:11:35\"}\n", "    **************:4999: {\"proxy\": \"**************:4999\", \"https\": false, \"fail_count\": 0, \"region\": \"error\", \"anonymous\": \"\", \"source\": \"freeProxy11\", \"check_count\": 3, \"last_status\": true, \"last_time\": \"2025-04-12 20:11:53\"}\n"]}], "source": ["import redis\n", "\n", "# 连接Redis\n", "r = redis.Redis(host='localhost', port=6379, db=0)\n", "\n", "# 获取 db0 里面的所有 key，字节数据，如：[b'use_proxy']\n", "keys = r.keys('*') \n", "\n", "for key in keys:\n", "    key_str = key.decode('utf-8')\n", "    \n", "    # 首先检查 key 的类型\n", "    key_type = r.type(key).decode('utf-8')\n", "    \n", "    print(f\"键: {key_str}, 类型: {key_type}\")\n", "    \n", "    try:\n", "        # 根据类型使用不同的命令获取值\n", "        if key_type == 'string':\n", "            value = r.get(key)\n", "            print(f\"  值: {value.decode('utf-8') if value else None}\")\n", "            \n", "        elif key_type == 'hash':\n", "            hash_data = r.h<PERSON><PERSON>(key)\n", "            print(f\"  哈希表内容:\")\n", "            for field, val in hash_data.items():\n", "                print(f\"    {field.decode('utf-8')}: {val.decode('utf-8')}\")\n", "                \n", "        elif key_type == 'list':\n", "            list_len = r.llen(key)\n", "            list_items = r.l<PERSON>e(key, 0, list_len - 1)\n", "            print(f\"  列表内容 (长度: {list_len}):\")\n", "            for i, item in enumerate(list_items):\n", "                print(f\"    {i}: {item.decode('utf-8')}\")\n", "                \n", "        elif key_type == 'set':\n", "            set_items = r.smembers(key)\n", "            print(f\"  集合内容 (大小: {len(set_items)}):\")\n", "            for item in set_items:\n", "                print(f\"    {item.decode('utf-8')}\")\n", "                \n", "        elif key_type == 'zset':\n", "            zset_items = r.zrange(key, 0, -1, withscores=True)\n", "            print(f\"  有序集合内容 (大小: {len(zset_items)}):\")\n", "            for item, score in zset_items:\n", "                print(f\"    {item.decode('utf-8')}: {score}\")\n", "                \n", "        else:\n", "            print(f\"  不支持的类型: {key_type}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"  处理键 {key_str} 时出错: {e}\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["键: use_proxy, 类型: hash\n", "哈希表内容:\n", "https://**********:3128\n", "http://**************:4999\n"]}], "source": ["import redis\n", "import json\n", "\n", "# 连接 Redis 并全局启用 UTF-8 解码\n", "r = redis.Redis(\n", "    host='localhost',\n", "    port=6379,\n", "    db=0,\n", "    decode_responses=True  # 关键参数：自动解码字节为字符串\n", ")\n", "\n", "# # 获取所有键（直接返回字符串）\n", "# keys = r.keys('*')\n", "# # for key in keys:\n", "# key_type = r.type(key)  # 直接返回字符串类型\n", "\n", "\n", "key_name = 'use_proxy'\n", "key_type = r.type(key_name)\n", "\n", "print(f\"键: {key_name}, 类型: {key_type}\")\n", "\n", "try:\n", "    if key_type == 'string':\n", "        value = r.get(key_name)\n", "        print(f\"值: {value}\")\n", "        \n", "    elif key_type == 'hash':\n", "        hash_data = r.h<PERSON>all(key_name)\n", "        print(f\"哈希表内容:\")\n", "        for field, val in hash_data.items():\n", "            # print(f\"{field}: {val}\")  # field 和 val 已解码\n", "            # print(json.loads(val))\n", "            if json.loads(val)['https']:  # 字符串转dict，true转True\n", "                print('https://' + field)\n", "            else:\n", "                print('http://' + field)\n", "            \n", "    elif key_type == 'list':\n", "        list_items = r.l<PERSON>e(key_name, 0, -1)\n", "        print(f\"列表内容 (长度: {len(list_items)}):\")\n", "        for idx, item in enumerate(list_items):\n", "            print(f\"{idx}: {item}\")  # item 已解码\n", "            \n", "    elif key_type == 'set':\n", "        set_items = r.smembers(key_name)\n", "        print(f\"集合内容 (大小: {len(set_items)}):\")\n", "        for item in set_items:\n", "            print(f\"{item}\")  # item 已解码\n", "            \n", "    elif key_type == 'zset':\n", "        zset_items = r.zrange(key_name, 0, -1, withscores=True)\n", "        print(f\"有序集合内容 (大小: {len(zset_items)}):\")\n", "        for item, score in zset_items:\n", "            print(f\"{item}: {score}\")  # item 已解码\n", "            \n", "    else:\n", "        print(f\"不支持的类型: {key_type}\")\n", "        \n", "except Exception as e:\n", "    print(f\"处理键 {key_name} 时出错: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "webcrawl_py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}