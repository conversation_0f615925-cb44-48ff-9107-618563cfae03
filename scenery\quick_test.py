# -*- coding: utf-8 -*-
"""
快速测试修改后的格式验证函数
"""

import re

def format_output(scenery_final: str) -> bool:
    """
    目的：判断 combine_scenry() 函数的输出格式是否符合要求。"景点1|景点2|景点3"，优化后更宽松
    输入：待判断格式的str
    输出：True or False
    """
    if not scenery_final or scenery_final.strip() == "":
        return True  # 空字符串也是合法的

    # 去除首尾空白字符和可能的首尾"|"
    cleaned = scenery_final.strip().strip('|')

    if not cleaned:
        return True  # 清理后为空也是合法的

    # 检查是否没有连续的"|"
    if '||' in scenery_final:
        return False

    # 分割景点并检查每个景点
    spots = [spot.strip() for spot in cleaned.split('|') if spot.strip()]
    
    if not spots:
        return True  # 没有有效景点也是合法的
    
    # 检查每个景点名称
    for spot in spots:
        # 允许中文、数字、常见标点符号
        # 特殊处理：允许一些英文字母（如798艺术区、K11等）
        if not re.match(r'^[\u4e00-\u9fff\d\w（）()·\-\s]+$', spot):
            return False
        
        # 景点名称不能太短或太长
        if len(spot) < 1 or len(spot) > 20:
            return False
    
    return True

def test_format_validation():
    """测试格式验证"""
    test_cases = [
        ("广州|佛山|珠海", True, "基本格式"),
        ("故宫|天安门|王府井", True, "标准景点"),
        ("", True, "空字符串"),
        ("广州", True, "单个景点"),
        ("广州||佛山", False, "连续分隔符"),
        ("广州|佛山|", True, "末尾分隔符"),
        ("|广州|佛山", True, "开头分隔符"),
        ("798艺术区|K11", True, "包含英文数字"),
        ("广州|佛山|珠海|光孝寺|白云山", True, "多个景点"),
        ("广州|佛山|珠海|光孝寺|白云山|佛山祖庙|叶问堂|黄飞鸿纪念馆", True, "很多景点"),
    ]
    
    print("=== 格式验证测试 ===")
    for test_input, expected, description in test_cases:
        result = format_output(test_input)
        status = "✓" if result == expected else "✗"
        print(f"{status} {description}: '{test_input}' -> {result} (期望: {expected})")

if __name__ == "__main__":
    test_format_validation()
