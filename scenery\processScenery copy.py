"""
1. 逐行从游记正文中读取信息：scenery、processed_text，
2. 将 processed_text 传入 extract_scenery() 抽取景点，返回 extract_scenery
3. 综合 scenery 和 extract_scenery 信息，输入给大模型，合并、去重、规范格式 scenery_final
4. 检查 scenery_final 是否满足 "景点1|景点2|景点3" 的格式，如果不满足，重新进行第 3 步，最大重试次数为3次，
5. 将 scenery_final 写入原csv 的 scenery_final 列

1 条记录处理，大概 3000 tokens，跑完所有记录，大概需要耗费 10M tokens
"""


import os
import pandas as pd
import time
import logging
from logging.handlers import RotatingFileHandler
from pypinyin import lazy_pinyin
from tqdm import tqdm
import re
from datetime import datetime

from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv())

from zhipuai import ZhipuAI

client = ZhipuAI(api_key=os.environ["ZHIPUAI_API_KEY"])

# 配置日志系统
def setup_logger():
    """
    配置日志系统，输出到文件和控制台
    """
    # 创建logs目录
    if not os.path.exists('scenery/logs'):
        os.makedirs('scenery/logs')
    
    # 创建logger
    logger = logging.getLogger('scenery_processor')
    logger.setLevel(logging.INFO)
    
    # 避免重复添加handler
    if logger.handlers:
        logger.handlers.clear()
    
    # 使用固定的日志文件名，支持追加模式
    log_file = 'scenery/logs/scenery_process.log'
    
    # 创建文件handler，使用追加模式
    file_handler = RotatingFileHandler(log_file, maxBytes=1024*1024, backupCount=3, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建formatter，包含更详细的信息
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加handler到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 在每次启动时记录分隔符，便于区分不同的运行会话
    separator = "=" * 80
    startup_msg = f"程序启动 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    logger.info(separator)
    logger.info(startup_msg)
    logger.info(separator)
    
    return logger

# 初始化logger
logger = setup_logger()

def gen_glm_params(prompt):
    '''
    构造 GLM 模型请求参数 messages

    请求参数：
        prompt: 对应的用户提示词
    '''
    messages = [{"role": "user", "content": prompt}]
    return messages


def get_completion(prompt, model="glm-4-plus", temperature=0.95, max_retries=3, row_index=None):
    '''
    获取 GLM 模型调用结果

    请求参数：
        prompt: 对应的提示词
        model: 调用的模型，默认为 glm-4-plus，也可以按需选择 glm-3-turbo 等其他模型
        temperature: 模型输出的温度系数，控制输出的随机程度，取值范围是 0.0-1.0。温度系数越低，输出内容越一致。
        max_retries: 最大重试次数
        row_index: 当前处理的行索引，用于日志输出
    '''

    messages = gen_glm_params(prompt)
    
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature
            )
            if len(response.choices) > 0:
                return response.choices[0].message.content
            else:
                if row_index is not None:
                    logger.warning(f"第{row_index + 1}行记录：模型返回空结果，第 {attempt + 1} 次重试")
                else:
                    logger.warning(f"模型返回空结果，第 {attempt + 1} 次重试")
                
        except Exception as e:
            if row_index is not None:
                logger.error(f"第{row_index + 1}行记录：API调用失败 (第 {attempt + 1} 次): {e}")
            else:
                logger.error(f"API调用失败 (第 {attempt + 1} 次): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            
    return "generate answer error"

# logger.info(get_completion("你好！"))


def extract_scenery(content: str, row_index: int = None) -> str:
    """
    输入：待提取景点信息的文本内容
    输出：文本中的景点，str格式
    """
    if pd.isna(content) or content.strip() == "":
        return ""

    prompt = f"""
    # Role: 旅游景点识别专家

    ## Goals
    - 对给定的旅行游记进行旅游景点识别。

    ## Constrains
    - 输出结果格式为："景点1|景点2|景点3"，不能包含任何其他多余文字
    - 如果没有识别到景点，请返回空字符串
    - 景点名称应为标准名称，如"故宫"而非"故宫博物院"
    - 景点中不能包含有英文字母

    ## Example:
    - 输入："今天去了天安门广场，然后参观了故宫博物院，最后在王府井大街购物"
    - 输出："天安门广场|故宫|王府井大街"

    ## Workflow
    1. 读取并理解给定的文本内容：{content}
    2. 输出识别的景点
    """
    
    try:
        scenery = get_completion(prompt, model='glm-4-plus', temperature=0.5, row_index=row_index)
        return scenery.strip()
    except Exception as e:
        if row_index is not None:
            logger.error(f"第{row_index + 1}行记录：景点提取失败: {e}")
        else:
            logger.error(f"景点提取失败: {e}")
        return ""


def format_output(scenery_final:str)->bool:
    """
    目的：判断 combine_scenry() 函数的输出格式是否符合要求。"景点1|景点2|景点3"，不对文本最前面和最后面是否有"|"做约束
    输入：待判断格式的str
    输出：True or False
    """
    if not scenery_final or scenery_final.strip() == "":
        return True  # 空字符串也是合法的
    
    # 去除首尾空白字符和可能的首尾"|"
    cleaned = scenery_final.strip().strip('|')
    
    if not cleaned:
        return True  # 清理后为空也是合法的
    
    # 检查是否包含英文字母（不允许）
    if re.search(r'[a-zA-Z]', cleaned):
        return False
    
    # 检查是否没有连续的"|"
    if '||' in scenery_final:
        return False
    
    # 检查格式：只允许中文、数字、中文括号和"|"分隔符
    # 景点名称可能包含数字（如"798艺术区"）和中文括号
    pattern = r'^[\u4e00-\u9fff\d（）\s]+(\|[\u4e00-\u9fff\d（）\s]+)*$'
    
    return bool(re.match(pattern, cleaned))

def combine_scenery(index, scenery, extract_scenery_result, max_retries=3) -> str:
    """
    目的：合并 scenery 和 extract_scenery 的景点，去重，规范输出格式。
    输入：景点、抽取的景点。类型：str
    输出：合并、去重、规范输出格式的景点。样例："景点1|景点2|景点3"
    """
    
    # 处理输入为空的情况
    scenery = scenery if pd.notna(scenery) and scenery.strip() else ""
    extract_scenery_result = extract_scenery_result if extract_scenery_result and extract_scenery_result.strip() else ""
    
    # 如果两个都为空，直接返回空字符串
    if not scenery and not extract_scenery_result:
        return ""
    
    prompt = f"""
    # Role: 景点信息合并专家

    ## Goals
    - 合并两个景点列表，去重并规范输出格式

    ## Constrains
    - 输出格式严格为："景点1|景点2|景点3"
    - 去除重复景点
    - 景点名称使用标准简称
    - 不能包含任何其他文字说明
    - 景点中不能包含英文字母
    - 如果没有景点，返回空字符串

    ## Input Data
    原始景点列表：{scenery}
    提取的景点列表：{extract_scenery_result}

    ## Example
    - 输入：原始="故宫|天安门" 提取="故宫博物院|王府井"
    - 输出："故宫|天安门|王府井"

    请直接输出合并去重后的景点列表：
    """
    
    tryNum = 0
    while tryNum < max_retries:
        try:
            scenery_final = get_completion(prompt, model='glm-4-plus', temperature=0.3, row_index=index)
            scenery_final = scenery_final.strip()
            
            if format_output(scenery_final):
                if tryNum > 0:
                    logger.info(f"第{index + 1}行记录：重试成功，最终结果：{scenery_final}")
                return scenery_final
            else:
                logger.warning(f"第{index + 1}行记录：格式验证失败，第{tryNum + 1}次重试。结果：{scenery_final}")
                tryNum += 1
        except Exception as e:
            logger.error(f"第{index + 1}行记录：合并景点失败，第{tryNum + 1}次重试：{e}")
            tryNum += 1
    
    # 重试失败，返回原始数据
    logger.warning(f"第{index + 1}行记录：达到最大重试次数，返回原始景点数据")
    # return scenery if scenery else extract_scenery_result
    return extract_scenery_result if extract_scenery_result else scenery


def main(csv_path, output_path):
    """
    主函数，处理整个流程
    """
    logger.info(f"🚀 开始处理CSV文件: {csv_path}")

    try:
        # header_df = pd.read_csv(csv_path, encoding='utf-8-sig', nrows=1)
        # column_name = header_df.columns.tolist()

        # df = pd.read_csv(csv_path, encoding='utf-8-sig', skiprows=1498, names=column_name)
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        logger.info(f"📊 成功读取数据，共 {len(df)} 行")
    except Exception as e:
        logger.error(f"❌ 读取CSV文件失败: {e}")
        return
    
    print(df.columns)
    print(df.head(2))

    # 检查必要的列是否存在
    required_columns = ['scenery', 'processed_text']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"❌ 缺少必要的列: {missing_columns}")
        return
    
    df['scenery_final'] = ""
    
    # 统计变量
    success_count = 0
    error_count = 0
    retry_count = 0

    # 使用tqdm显示进度条
    for i in tqdm(range(len(df)), desc="处理景点数据", unit="行"):
        try:
            scenery = df.loc[i, 'scenery']
            content = df.loc[i, 'processed_text'][:4000]
            
            # 记录开始处理
            logger.debug(f"开始处理第{i + 1}行记录")
            
            # 提取景点
            extract_scenery_result = extract_scenery(content, i)
            
            # 合并景点
            scenery_final = combine_scenery(i, scenery, extract_scenery_result, max_retries=3)
            
            df.at[i, 'scenery_final'] = scenery_final
            success_count += 1
            
            # 记录处理结果
            logger.debug(f"第{i + 1}行处理完成：{scenery_final}")
            
            # 每处理100行输出一次状态
            if (i + 1) % 100 == 0:
                logger.info(f"✅ 已处理 {i + 1} 行，成功: {success_count}, 失败: {error_count}")
                
        except Exception as e:
            logger.error(f"❌ 处理第 {i + 1} 行时出错: {e}")
            df.at[i, 'scenery_final'] = df.loc[i, 'scenery'] if pd.notna(df.loc[i, 'scenery']) else ""
            error_count += 1
    
    # 保存结果
    try:
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        logger.info(f"💾 结果已保存到: {output_path}")
        logger.info(f"📈 处理完成！成功: {success_count}, 失败: {error_count}")
        
        # 显示一些样例结果
        logger.info("📋 处理结果样例:")
        sample_df = df[df['scenery_final'].notna() & (df['scenery_final'] != "")].head(3)
        for idx, row in sample_df.iterrows():
            logger.info(f"  行 {idx + 1}: {row['scenery_final']}")
            
    except Exception as e:
        logger.error(f"❌ 保存文件失败: {e}")


if __name__ == "__main__":
    csv_path = 'D:\Code\PythonCode\scrape\scenery\data_filtered_fenci.csv'
    final_csv = 'D:\Code\PythonCode\scrape\scenery\data_filtered_fenci_scenery.csv'
    main(csv_path, final_csv)