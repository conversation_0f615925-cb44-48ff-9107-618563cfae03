"""
整合 data/csv 文件夹下的所有 csv 文件到 data.csv 中，根据 csv 中的 「游记id」、「作者id」去除重复行
"""

import os
import pandas as pd
import glob

def combine_csv_files():
    """整合data/csv文件夹下的所有CSV文件并去除重复行"""
    
    # 设置CSV文件夹路径
    csv_folder = "data/csv"
    
    # 确保文件夹存在
    if not os.path.exists(csv_folder):
        print(f"错误: {csv_folder} 文件夹不存在")
        return
    
    # 获取文件夹下所有CSV文件的路径
    csv_files = glob.glob(os.path.join(csv_folder, "*.csv"))
    
    if not csv_files:
        print(f"错误: 在 {csv_folder} 中没有找到CSV文件")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件")
    
    # 创建空列表存储所有数据框
    all_dfs = []
    
    # 读取每个CSV文件并添加到列表中
    for file in csv_files:
        try:
            df = pd.read_csv(file, encoding='utf-8')
            print(f"读取文件: {file}, 行数: {len(df)}")
            all_dfs.append(df)
        except Exception as e:
            print(f"读取文件 {file} 时出错: {e}")
            # 尝试使用不同的编码
            try:
                df = pd.read_csv(file, encoding='gbk')
                print(f"使用GBK编码读取文件: {file}, 行数: {len(df)}")
                all_dfs.append(df)
            except Exception as e2:
                print(f"使用GBK编码读取文件 {file} 时仍然出错: {e2}")
    
    if not all_dfs:
        print("没有成功读取任何CSV文件")
        return
    
    # 合并所有数据框
    combined_df = pd.concat(all_dfs, ignore_index=True)
    print(f"合并后总行数: {len(combined_df)}")
    
    # 检查是否存在所需的列
    required_columns = ["游记id", "作者id"]
    missing_columns = [col for col in required_columns if col not in combined_df.columns]
    
    if missing_columns:
        print(f"警告: 缺少以下列: {missing_columns}")
        print("可用的列有: ", combined_df.columns.tolist())
    else:
        # 根据"游记id"和"作者id"去除重复行
        combined_df = combined_df.drop_duplicates(subset=["游记id", "作者id"])
        print(f"去重后行数: {len(combined_df)}")
    
    # 确保输出目录存在
    output_dir = "data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存合并后的数据到data.csv
    output_file = os.path.join(output_dir, "data.csv")
    combined_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"数据已保存到 {output_file}")

if __name__ == "__main__":
    combine_csv_files()
