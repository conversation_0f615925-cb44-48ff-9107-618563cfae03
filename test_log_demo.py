"""
演示改进后的日志功能
"""

import importlib.util

# 动态导入模块
spec = importlib.util.spec_from_file_location("processScenery_copy", "./scenery/processScenery copy.py")
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)

logger = module.logger

def demo_logging_features():
    """
    演示日志功能特性
    """
    
    logger.info("📝 演示日志功能特性")
    logger.info("1. 所有日志都会写入同一个文件: scenery/logs/scenery_process.log")
    logger.info("2. 每次启动会用分隔符区分不同的运行会话")
    logger.info("3. 支持日志轮转：当文件超过1MB时会自动创建备份文件")
    
    logger.warning("⚠️  这是一个警告信息")
    logger.error("❌ 这是一个错误信息")
    
    logger.info("✅ 日志功能演示完成")

if __name__ == "__main__":
    demo_logging_features() 