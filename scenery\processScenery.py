"""
输入一个含有 景点 列的 csv 文件，调用大模型 api 清洗 景点 列数据

逐个记录输入到大模型中，然后返回清洗后的结果，将结果写入原 csv 的新列 scenery 中

读取 scenery 列中所有景点，按拼音排序并拼接，设置滑动窗口分批次输入给大模型，窗口大小为100个景点，步长80，让大模型识别相同但名字有差异的景点，构建景点匹配映射表

对清洗后的 scenery 列中的错误景点做映射处理


这种问题场景，推荐使用 llm+rag 的方案，rag作为一个景点库，根据景点的拼音排序匹配

运行 1 次，0.1M tokens

---

GLM-4-Plus	高智能旗舰: 性能全面提升，长文本和复杂任务能力显著增强	128K	4K

https://www.bigmodel.cn/dev/howuse/prompt 信息抽取

"""

import os
import pandas as pd
import time
from pypinyin import lazy_pinyin
from tqdm import tqdm

from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv())

from zhipuai import ZhipuAI


client = ZhipuAI(api_key=os.environ["ZHIPUAI_API_KEY"])

def gen_glm_params(prompt):
    '''
    构造 GLM 模型请求参数 messages

    请求参数：
        prompt: 对应的用户提示词
    '''
    messages = [{"role": "user", "content": prompt}]
    return messages


def get_completion(prompt, model="glm-4-plus", temperature=0.95):
    '''
    获取 GLM 模型调用结果

    请求参数：
        prompt: 对应的提示词
        model: 调用的模型，默认为 glm-4，也可以按需选择 glm-3-turbo 等其他模型
        temperature: 模型输出的温度系数，控制输出的随机程度，取值范围是 0.0-1.0。温度系数越低，输出内容越一致。
    '''

    messages = gen_glm_params(prompt)
    response = client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature
    )
    if len(response.choices) > 0:
        return response.choices[0].message.content
    return "generate answer error"

# print(get_completion("你好！"))


def clean_single_scenery(scenery):
    """
    清洗单个景点名称
    """
    if pd.isna(scenery) or scenery.strip() == "":
        return ""
    
    prompt = f"""
    请帮我清洗以下景点名称，去除无关信息，保留核心景点名称：
    {scenery}
    
    要求：
    1. 返回格式为"景点1|景点2|景点3"，不要有任何解释或其他文字
    2. 如果不是景点，请返回空字符串
    3. 去除票价、地址、电话等无关信息
    4. 景点名称应为标准名称，如"故宫"而非"故宫博物院"
    """
    
    try:
        result = get_completion(prompt, temperature=0.5)
        return result.strip()
    except Exception as e:
        print(f"清洗景点'{scenery}'时出错: {e}")
        return scenery

def process_csv(csv_path):
    """
    处理CSV文件，清洗景点列数据
    """
    # 读取CSV文件
    df = pd.read_csv(csv_path, encoding='utf-8-sig')

    column_mapping = {'scenery': '景点'}

    # 修改列名
    df = df.rename(columns=column_mapping)

    if '景点' not in df.columns:
        raise ValueError("CSV文件中没有'景点'列")
    
    # 创建新列用于存储清洗后的结果
    df['scenery'] = ""
    
    # 逐行处理景点数据
    print("正在清洗景点数据...")
    for idx, row in tqdm(df.iterrows(), total=len(df)):
        cleaned_scenery = clean_single_scenery(row['景点'])
        df.at[idx, 'scenery'] = cleaned_scenery
        # 避免API调用过于频繁
        time.sleep(0.5)
    
    # 保存中间结果
    # intermediate_csv = csv_path.replace('.csv', '_cleaned.csv')
    # df.to_csv(intermediate_csv, index=False, encoding='utf-8-sig')  # 将清洗后的 df 保存为 csv 文件
    # print(f"清洗完成，中间结果已保存至: {intermediate_csv}")
    
    return df
    # return df, intermediate_csv

def create_scenery_mapping(df):
    """
    创建景点映射表，识别名称有差异但实际相同的景点
    """
    # 获取所有非空景点并拆分
    all_sceneries = []
    
    # 对scenery列中的每个单元格，以"|"拆分景点
    for cell in df['scenery'].dropna():
        if isinstance(cell, str) and cell.strip() != "":
            items = cell.split("|")
            # 添加每个拆分后的景点到列表
            all_sceneries.extend([item.strip() for item in items if item.strip() != ""])
    
    # 去重
    all_sceneries = list(set(all_sceneries))
    print("all_sceneries", all_sceneries)
    
    # 按拼音排序
    all_sceneries.sort(key=lambda x: ''.join(lazy_pinyin(x)))
    print("all_sceneries", all_sceneries)

    # 设置滑动窗口参数
    window_size = 100
    step_size = 80
    
    mapping = {}
    
    # 使用滑动窗口处理景点
    for i in range(0, len(all_sceneries), step_size):
        window = all_sceneries[i:i+window_size]
        if not window:
            continue
            
        window_text = "|".join(window)
        
        prompt = f"""
        以下是一些景点名称，使用"|"符号分隔，请识别其中名称不同但实际指同一景点的情况，返回一个映射表：
        
        {window_text}
        
        请返回JSON格式的映射表，格式为：
        {{
            "非标准名称1": "标准名称1",
            "非标准名称2": "标准名称1",
            "非标准名称3": "标准名称2",
            ...
        }}
        
        只返回有映射关系的景点，相同景点的不同表述应映射到同一个标准名称。
        """
        
        try:
            result = get_completion(prompt, temperature=0.3)
            # 提取JSON部分
            import json
            import re
            
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            print(json_match)
            if json_match:
                json_str = json_match.group(0)
                print(json_str)
                try:
                    window_mapping = json.loads(json_str)
                    print("window_mapping", window_mapping)
                    mapping.update(window_mapping)
                    print("window_mapping", window_mapping)
                except json.JSONDecodeError:
                    print(f"无法解析JSON: {json_str}")
            
            # 避免API调用过于频繁
            time.sleep(1)
        except Exception as e:
            print(f"创建映射表时出错: {e}")
    
    return mapping

def apply_mapping(df, mapping, final_csv):
    """
    应用映射表修正清洗后的景点名称
    """
    # 创建新列存储最终结果
    df['scenery_final'] = df['scenery']
    
    # 应用映射
    for idx, row in df.iterrows():
        scenery = row['scenery']
        print("scenery", scenery)
        if not isinstance(scenery, str) or scenery.strip() == "":
            continue
            
        # 处理可能包含多个以"|"分隔的景点
        if "|" in scenery:
            items = scenery.split("|")
            mapped_items = []
            for item in items:
                item = item.strip()
                print("item", item)
                if item:
                    # 如果在映射表中找到对应项，则使用映射值，否则保持原值
                    mapped_item = mapping.get(item, item)  # 如果在mapping中有item，那么返回对应的键，否则返回item（第二个item）
                    mapped_items.append(mapped_item)
            # 重新组合映射后的景点
            df.at[idx, 'scenery_final'] = "|".join(mapped_items)
        else:
            # 单个景点的情况
            df.at[idx, 'scenery_final'] = mapping.get(scenery.strip(), scenery.strip())
    
    # 保存最终结果
    # final_csv = intermediate_csv.replace('_cleaned.csv', '_final.csv')
    
    df.to_csv(final_csv, index=False, encoding='utf-8-sig')
    print(f"映射应用完成，最终结果已保存至: {final_csv}")
    
    return df, final_csv

def main(csv_path, final_csv):
    """
    主函数，处理整个流程
    """
    print(f"开始处理CSV文件: {csv_path}")
    
    # 1. 清洗景点数据
    # df, intermediate_csv = process_csv(csv_path)
    df = process_csv(csv_path)

    # 2. 创建景点映射表
    print("正在创建景点映射表...")
    mapping = create_scenery_mapping(df)
    print(f"映射表创建完成，共有{len(mapping)}个映射关系")
    
    # 3. 应用映射表
    df_final, final_csv = apply_mapping(df, mapping, final_csv)
    
    print("所有处理已完成!")
    return df_final, final_csv

if __name__ == "__main__":
    # import sys
    
    # if len(sys.argv) < 2:
    #     print("使用方法: python processScenery.py <csv文件路径>")
    #     sys.exit(1)
        
    # csv_path = sys.argv[1]
    csv_path = 'D:\Code\PythonCode\scrape\scenery\data_filtered.csv'
    final_csv = 'D:\Code\PythonCode\scrape\scenery\data_filtered_scenery.csv'
    main(csv_path, final_csv)