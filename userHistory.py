"""
对 data/userFans.csv 做筛选，保留 粉丝数>20，关注数>5 的记录，使用dataframe存储

然后获取dataframe的第一列 用户id（user_id），按照规则：https://travel.qunar.com/space/notes/{user_id}@qunar"拼接用户主页的url

爬取历史游记

首先解析 div class="timeline" 下面<ul class="timeline-ct"> <li class="item"> 标签

每一篇历史游记都存放在 <li class="item"> 标签中

获取下面的日期作为[游记发表日期]
<dl class="timeline-date"><dt>04/15</dt><dd>2019</dd></dl>

然后 <div class="timeline-content"> 中 <dl class="noteitem"> 标签中 <dd class="title">，获取[链接]，并获取文本作为[标题]
<dd class="title"><a href="//travel.qunar.com/youji/7469823" target="_blank"
                            data-beacon="youji-title">#游记征集令#这个春天一路向西，东北小伙6天7晚大西北自由行</a></dd>

其次，获取这里的日期作为[出发日期]，天数作为[游记天数]，图片数量作为[游记图片数量]
<div class="cdate">2019-04-05出发<span class="gray">|</span>共6天<span class="gray">|</span>517图


最后根据下面信息，提取[点赞数]
<div class="stat"><ul class="clrfix"><li class="comment">0</li><li class="zan">32</li><li class="pv">127191</li></ul></div>


最终返回一个dataframe，包含以下列：
用户id, 游记标题, 游记链接, 出发日期, 游记发表日期, 游记天数, 游记图片数量, 点赞数


#TODO
1. 先测试一下 
2. 如果<li class="item"> 超过10个，需要模拟鼠标点击"全部游记"，并随着鼠标滚动才会加载所有游记，请实现

<div class="btnbox"><a href="//travel.qunar.com/space/notes/*********@qunar" class="">全部游记</a><a href="//travel.qunar.com/space/books/*********@qunar" class="">全部行程</a><a href="//travel.qunar.com/space/smartlist/*********@qunar" class="">全部榜单</a></div>

处理速度：320个/h

"""

import os
import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import logging
import datetime
import random
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 设置工作目录
# os.chdir("/Data/wangsipeng/SMPP/crawl")
# os.chdir("/root/autodl-tmp/crawl")
print("当前工作目录：", os.getcwd())

# 创建日志目录
os.makedirs("logs/userHistory", exist_ok=True)

# 设置日志
def setup_logger():
    """设置日志记录器"""
    # 生成日志文件名，格式为：userHistory_年月日时分秒.log
    current_time = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    log_file = f"logs/userHistory/userHistory_{current_time}.log"
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('userHistory')

# 添加多个User-Agent列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/2.5.0.298",
    "Safari/macOS: Mozilla/5.0 (Macintosh; Intel Mac OS X 11_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.2 Safari/605.1.15",
    "Opera/Windows: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 OPR/73.0.3856.329",
    "Vivaldi/Windows: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36 Vivaldi/3.5",
    "Internet-Explorer/Windows: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.215 Safari/537.36 TBC/1.3.3.999 Thunder/12.1.6.2780"
]

def fetchUrl(logger, url, max_retries=3):
    """获取URL内容，包含重试机制"""
    retries = 0

    while retries < max_retries:
        try:
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--log-level=3')  # 只显示严重错误
            
            # 使用webdriver_manager自动下载和管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            browser = webdriver.Chrome(service=service, options=chrome_options)
            
            browser.get(url)
            time.sleep(5)
            
            # 等待timeline-ct内的元素加载
            WebDriverWait(browser, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "ul.timeline-ct li.item"))
            )
            
            # TODO 如果游记数量超过10个，可能需要加载更多，模拟点击和滚动！
            # # 检查游记项目数量
            # items = browser.find_elements(By.CSS_SELECTOR, "ul.timeline-ct li.item")
            # logger.info(f"初始加载游记数量: {len(items)}")
            
            # # 如果游记数量超过10个，可能需要加载更多
            # if len(items) >= 10:
            #     try:
            #         # 尝试查找并点击"全部游记"按钮
            #         all_notes_btn = WebDriverWait(browser, 5).until(
            #             EC.element_to_be_clickable((By.XPATH, "//div[contains(@class,'btnbox')]/a[contains(text(),'全部游记')]"))
            #         )
            #         logger.info("找到'全部游记'按钮，准备点击")
            #         all_notes_btn.click()
            #         time.sleep(3)  # 等待页面加载
                    
            #         # 滚动加载所有游记
            #         last_height = browser.execute_script("return document.body.scrollHeight")
            #         scroll_count = 0
            #         max_scrolls = 30  # 设置最大滚动次数，防止无限循环
                    
            #         while scroll_count < max_scrolls:
            #             # 滚动到页面底部
            #             browser.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            #             time.sleep(2)  # 等待内容加载
                        
            #             # 计算新的滚动高度
            #             new_height = browser.execute_script("return document.body.scrollHeight")
                        
            #             # 如果高度没有变化，说明没有更多内容加载，跳出循环
            #             if new_height == last_height:
            #                 scroll_count += 1
            #                 # 如果连续3次高度未变，认为已加载完毕
            #                 if scroll_count >= 3:
            #                     break
            #             else:
            #                 scroll_count = 0  # 重置计数器
            #                 last_height = new_height
                    
            #         # 再次获取所有游记项目
            #         items = browser.find_elements(By.CSS_SELECTOR, "ul.timeline-ct li.item")
            #         logger.info(f"滚动加载后游记数量: {len(items)}")
                    
            #     except Exception as e:
            #         logger.warning(f"加载全部游记时出错: {e}")
            
            # 获取完整页面源码
            html = browser.page_source
            browser.quit()
            return html
            
        except Exception as e:
            logger.warning(f"获取页面出错: {e}")
            retries += 1
    
    return None

def parseUserHistory(logger, html, user_id, max_retries=3):
    """解析HTML获取用户历史游记"""
    retries = 0

    while retries < max_retries:
        try:
            # 解析HTML网页
            bsObj = BeautifulSoup(html, "html.parser")
            
            # 查找timeline容器
            timeline = bsObj.find("div", attrs={"class": "timeline"})
            if not timeline:
                logger.warning("未找到'timeline'容器，尝试重试...")
                retries += 1
                continue

            # 查找所有游记项目
            items = timeline.find("ul", class_="timeline-ct").find_all("li", class_="item")
            if not items:
                logger.warning(f"未找到游记项目，尝试重试...")
                retries += 1
                continue
            
            logger.info(f"找到 {len(items)} 篇游记")
            results = []
            
            # 遍历每一个游记项目
            for item in items:
                try:
                    # 1. 获取游记发表日期
                    date_element = item.find("dl", class_="timeline-date")
                    if date_element:
                        month_day = date_element.find("dt").text.strip()  # 例如：04/15
                        year = date_element.find("dd").text.strip()  # 例如：2019
                        publish_date = f"{year}/{month_day}"  # 格式为 年/月/日，如：2019/04/15
                    else:
                        publish_date = ""
                    
                    # 2. 获取游记标题和链接
                    title_element = item.find("dd", class_="title")
                    if title_element and title_element.find("a"):
                        title = title_element.find("a").text.strip()
                        link = title_element.find("a").get("href", "")
                        # 处理相对链接
                        if link.startswith("//"):
                            link = "https:" + link
                    else:
                        title = ""
                        link = ""
                    
                    # 3. 获取出发日期、游记天数和图片数量
                    cdate_element = item.find("div", class_="cdate")
                    if cdate_element:
                        cdate_text = cdate_element.get_text()
                        
                        # 解析出发日期
                        departure_match = re.search(r"(\d{4}-\d{2}-\d{2})出发", cdate_text)
                        departure_date = departure_match.group(1).replace("-", "/") if departure_match else ""
                        
                        # 解析游记天数
                        days_match = re.search(r"共(\d+)天", cdate_text)
                        days = days_match.group(1) if days_match else ""
                        
                        # 解析图片数量
                        pics_match = re.search(r"(\d+)图", cdate_text)
                        pics_count = pics_match.group(1) if pics_match else ""
                    else:
                        departure_date = ""
                        days = ""
                        pics_count = ""
                    
                    # 4. 获取点赞数
                    stat_element = item.find("div", class_="stat")
                    if stat_element:
                        like_element = stat_element.find("li", class_="zan")
                        likes_count = like_element.text.strip() if like_element else "0"
                    else:
                        likes_count = "0"
                    
                    # 添加到结果列表
                    results.append({
                        "用户id": user_id,
                        "游记标题": title,
                        "游记链接": link,
                        "出发日期": departure_date,
                        "游记发表日期": publish_date,
                        "游记天数": days,
                        "游记图片数量": pics_count,
                        "点赞数": likes_count
                    })
                    
                except Exception as e:
                    logger.warning(f"解析单个游记项目时出错: {e}")
                    continue
            
            if results:
                logger.info(f"成功解析 {len(results)} 篇游记数据")
                return results
            else:
                logger.warning("未能解析出任何游记数据，尝试重试...")
                retries += 1
                
        except Exception as e:
            logger.warning(f"解析发生异常：{e}，尝试重试...")
            retries += 1

    # 如果达到最大重试次数仍未成功
    logger.error(f"解析用户历史游记失败: {user_id}")
    return []

def filterUserFans(logger):
    """筛选符合条件的用户"""
    try:
        # 检查userFans.csv是否存在
        if not os.path.exists("data/userFans.csv"):
            logger.error("userFans.csv文件不存在")
            return None
            
        # 读取userFans.csv文件
        logger.info("读取userFans.csv文件")
        df_fans = pd.read_csv("data/userFans.csv", encoding="utf_8_sig")
        
        # 检查必要的列是否存在
        required_columns = ["用户id", "粉丝数", "关注数"]
        for col in required_columns:
            if col not in df_fans.columns:
                logger.error(f"userFans.csv中缺少必要的列: {col}")
                return None
        
        # 转换粉丝数和关注数为数值类型
        df_fans["粉丝数"] = pd.to_numeric(df_fans["粉丝数"], errors="coerce")
        df_fans["关注数"] = pd.to_numeric(df_fans["关注数"], errors="coerce")
        
        # 筛选符合条件的用户: 粉丝数>20，关注数>5
        filtered_df = df_fans[(df_fans["粉丝数"] > 7) & (df_fans["关注数"] > 3)].copy()
        
        logger.info(f"筛选出符合条件的用户: {len(filtered_df)}个")
        return filtered_df
        
    except Exception as e:
        logger.error(f"筛选用户时发生错误: {e}")
        return None

def crawlUserHistory(logger):
    """爬取符合条件用户的历史游记"""
    try:
        # 筛选符合条件的用户
        filtered_df = filterUserFans(logger)
        if filtered_df is None or len(filtered_df) == 0:
            logger.error("没有符合条件的用户")
            return False
        
        # 获取所有符合条件的用户ID
        user_ids = filtered_df["用户id"].tolist()
        logger.info(f"开始爬取{len(user_ids)}个用户的历史游记")
        
        # 准备存储所有游记的列表
        all_notes = []
        total = len(user_ids)
        success = 0
        failed = 0
        
        # 遍历每一个用户ID，获取并存储历史游记
        for index, user_id in enumerate(user_ids):
            url = f"https://travel.qunar.com/space/notes/{user_id}@qunar"
            logger.info(f"处理第{index+1}/{total}个用户: {user_id}")
            
            # 获取HTML内容
            html = fetchUrl(logger, url)
            if not html:
                failed += 1
                logger.warning(f"获取用户{user_id}的页面失败")
                continue
            
            # 解析用户历史游记
            user_notes = parseUserHistory(logger, html, user_id)
            if user_notes:
                all_notes.extend(user_notes)
                success += 1
                logger.info(f"用户{user_id}成功获取{len(user_notes)}篇游记")
            else:
                failed += 1
                logger.warning(f"用户{user_id}未获取到游记")
            
            # 每处理10个用户保存一次，防止中途中断导致全部数据丢失
            if (index + 1) % 10 == 0 or (index + 1) == total:
                # 如果有数据，转换成DataFrame并保存
                if all_notes:
                    df_notes = pd.DataFrame(all_notes)
                    df_notes.to_csv("data/userHistory.csv", encoding="utf_8_sig", index=False)
                    logger.info(f"已保存进度 {index+1}/{total}，当前共{len(all_notes)}篇游记")
        
        # 打印统计信息
        logger.info("处理完成！")
        logger.info(f"总计: {total} 个用户")
        logger.info(f"成功: {success} 个")
        logger.info(f"失败: {failed} 个")
        logger.info(f"共获取: {len(all_notes)} 篇游记")
        
        # 如果有数据，转换成DataFrame返回
        if all_notes:
            return pd.DataFrame(all_notes)
        else:
            logger.warning("未获取到任何游记数据")
            return None
        
    except Exception as e:
        logger.error(f"爬取用户历史游记时发生错误: {e}")
        return None

def main():
    # 设置日志
    logger = setup_logger()
    logger.info("开始执行用户历史游记爬取")
    
    # 爬取用户历史游记
    df_history = crawlUserHistory(logger)
    
    # 保存最终结果
    if df_history is not None and not df_history.empty:
        df_history.to_csv("data/userHistory.csv", encoding="utf_8_sig", index=False)
        logger.info(f"用户历史游记数据保存完成，共{len(df_history)}条记录")
    else:
        logger.error("未能获取用户历史游记数据")
    
    logger.info("用户历史游记爬取任务完成")

if __name__ == "__main__":
    main() 