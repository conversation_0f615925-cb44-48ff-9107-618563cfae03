"""
测试脚本：验证改进后的日志系统和重试输出功能
"""

import pandas as pd
import sys
import os
import importlib.util

# 添加scenery目录到路径
sys.path.append('./scenery')

# 动态导入带空格的模块文件
spec = importlib.util.spec_from_file_location("processScenery_copy", "./scenery/processScenery copy.py")
module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(module)

# 从模块中导入需要的函数和对象
extract_scenery = module.extract_scenery
combine_scenery = module.combine_scenery
format_output = module.format_output
logger = module.logger

def test_logger_and_retry():
    """
    测试logger和重试功能
    """
    
    logger.info("🧪 开始测试logger和重试功能")
    
    # 测试1: 正常的景点提取
    logger.info("=" * 50)
    logger.info("测试1: 正常景点提取")
    
    test_content = "今天去了故宫博物院，然后参观了天安门广场，最后在王府井大街购物"
    row_index = 0
    
    result = extract_scenery(test_content, row_index)
    logger.info(f"测试1结果: {result}")
    
    # 测试2: 格式验证
    logger.info("=" * 50)
    logger.info("测试2: 格式验证功能")
    
    test_cases = [
        "故宫|天安门|王府井",  # 正确格式
        "故宫|天安门|Wang府井",  # 包含英文，错误格式
        "故宫||天安门",  # 连续分隔符，错误格式
        "",  # 空字符串，正确
        "故宫",  # 单个景点，正确
    ]
    
    for i, test_case in enumerate(test_cases):
        is_valid = format_output(test_case)
        logger.info(f"测试用例{i+1}: '{test_case}' -> {'✅有效' if is_valid else '❌无效'}")
    
    # 测试3: 合并景点功能（可能触发重试）
    logger.info("=" * 50)
    logger.info("测试3: 景点合并功能")
    
    scenery = "故宫|天安门"
    extract_result = "故宫博物院|王府井大街"
    row_index = 1
    
    combined_result = combine_scenery(row_index, scenery, extract_result, max_retries=2)
    logger.info(f"测试3结果: {combined_result}")
    
    logger.info("🎉 测试完成！请查看logs目录下的日志文件获取详细信息")

if __name__ == "__main__":
    test_logger_and_retry() 