"""
代理 ip 池接口，支持 txt、redis(127.0.0.1:5010)、list(Python)、dict(python)

输入：
 - txt 格式
   一行一个ip，如：http:127.0.0.1:1234
 - redis
   直接从读取 redis 中的 ip，127.0.0.1:5010/get
 - list。['http://proxy:port', 'http://proxy:port']
 - dict 如：{'http': 'proxy:port', 'https': 'proxy:port'}。整理为 list 格式

每种输入都使用一个函数处理，一共3个处理格式的函数（txt、redis、dict）
输出格式统一为：['http://proxy:port', 'http://proxy:port']

1个验证ip有效性的函数，如果有效那么更新 use_flag=1，否则=0

输出可用的 ip 池，list 格式，在sqlite中做持久化存储。
sqlite数据表 proxyIp[id, proxy, use_flag, create_datetime, update_datetime]，其中proxy为主键，存储形式为：'http://proxy:port'
use_flag可为空
"""

import os
import re
import sqlite3
import requests
import datetime
from typing import List, Dict, Union
import json
import redis

# 数据库初始化函数
def init_db(db_path='proxy_pool.db'):
    """初始化SQLite数据库"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建代理IP表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS proxyIp (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        proxy TEXT UNIQUE,
        use_flag INTEGER,
        create_datetime TEXT,
        update_datetime TEXT
    )
    ''')
    
    conn.commit()
    conn.close()

# 处理txt格式的代理IP
def load_from_txt(file_path: str) -> List[str]:
    """从txt文件中加载代理IP
    
    Args:
        file_path: txt文件路径
        
    Returns:
        代理IP列表 ['http://proxy:port', ...]
    """
    proxies = []
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return proxies
    
    with open(file_path, 'r') as f:
        for line in f:
            proxy = line.strip()
            if proxy.startswith('http://'):
                proxies.append(proxy)
            elif proxy.startswith('https://'):
                proxies.append(proxy)
            else:  # 将 list 中的元素逐个加入新的 list中
                proxies.extend([f'http://{proxy}', f'https://{proxy}'])  
    
    return proxies

# 从Redis加载代理IP
def load_from_redis(key_name: str = "use_proxy", host: str = "localhost", port: int = 6379, db: int = 0) -> List[str]:
    """从Redis中加载代理IP
    
    Args:
        key_name: Redis中存储代理的键名
        host: Redis服务器地址
        port: Redis服务器端口
        db: Redis数据库编号
        
    Returns:
        代理IP列表 ['http://proxy:port', ...]
    """
    proxies = []
    
    try:
        # 连接Redis并启用自动解码
        r = redis.Redis(host=host, port=port, db=db, decode_responses=True)
        
        # 获取键类型
        key_type = r.type(key_name)
        
        if key_type == 'string':
            # 如果是字符串类型，直接获取
            proxy = r.get(key_name)
            if proxy:
                # if not (proxy.startswith('http://') or proxy.startswith('https://')):
                #     proxy = f'http://{proxy}'
                # proxies.append(proxy)
                if proxy.startswith('http://'):
                    proxies.append(proxy)
                elif proxy.startswith('https://'):
                    proxies.append(proxy)
                else:  # 将 list 中的元素逐个加入新的 list中
                    proxies.extend([f'http://{proxy}', f'https://{proxy}'])  
                
        elif key_type == 'hash':
            # 如果是哈希类型，获取所有字段和值
            hash_data = r.hgetall(key_name)
            for field, val in hash_data.items():
                try:
                    # 解析JSON字符串为字典
                    json_data = json.loads(val)
                    # 根据https字段决定协议
                    if 'https' in json_data and json_data['https']:
                        proxy = f'https://{field}'
                    else:
                        proxy = f'http://{field}'
                    proxies.append(proxy)
                except json.JSONDecodeError:
                    # 如果不是JSON格式，使用默认http协议
                    proxy = f'http://{field}'
                    proxies.append(proxy)
                    
        elif key_type == 'list':
            # 如果是列表类型，获取所有元素
            list_items = r.lrange(key_name, 0, -1)
            for item in list_items:
                if not (item.startswith('http://') or item.startswith('https://')):
                    item = f'http://{item}'
                proxies.append(item)
                
        elif key_type == 'set':
            # 如果是集合类型，获取所有元素
            set_items = r.smembers(key_name)
            for item in set_items:
                if not (item.startswith('http://') or item.startswith('https://')):
                    item = f'http://{item}'
                proxies.append(item)
                
    except Exception as e:
        print(f"从Redis加载代理失败: {e}")
    
    return proxies

# 处理Python列表和字典格式的代理IP
def format_proxies(proxy_data: Union[List[str], Dict[str, str]]) -> List[str]:
    """格式化Python列表或字典格式的代理IP
    
    Args:
        proxy_data: 代理IP列表或字典
        
    Returns:
        代理IP列表 ['http://proxy:port', ...]
    """
    proxies = []
    
    if isinstance(proxy_data, list):
        for proxy in proxy_data:
            # if not (proxy.startswith('http://') or proxy.startswith('https://')):
            #     proxy = f'http://{proxy}'
            # proxies.append(proxy)
            if proxy.startswith('http://'):
                proxies.append(proxy)
            elif proxy.startswith('https://'):
                proxies.append(proxy)
            else:  # 将 list 中的元素逐个加入新的 list中
                proxies.extend([f'http://{proxy}', f'https://{proxy}'])  

    elif isinstance(proxy_data, dict):  
        for protocol, proxy in proxy_data.items():
            if not (proxy.startswith('http://') or proxy.startswith('https://')):
                # 根据协议类型添加前缀
                if protocol.lower() == 'https':
                    proxy = f'https://{proxy}'
                else:
                    proxy = f'http://{proxy}'
                proxies.append(proxy)
            
            if proxy not in proxies:
                proxies.append(proxy)
    
    return proxies

# 验证代理IP是否有效
def verify_proxy(proxy: str, test_url: str = "http://www.baidu.com", timeout: int = 30) -> bool:
    """验证代理IP是否有效
    
    Args:
        proxy: 代理IP地址，格式: 'http://proxy:port'
        test_url: 测试URL
        timeout: 超时时间(秒)
        
    Returns:
        代理是否有效
    """
    try:
        if not (proxy.startswith('http://') or proxy.startswith('https://')):
            proxy = f'http://{proxy}'
        
        # 确定协议类型
        protocol = 'https' if proxy.startswith('https://') else 'http'
            
        proxies = {
            "http": proxy if protocol == 'http' else None,
            "https": proxy if protocol == 'https' else proxy
        }
        
        response = requests.get(test_url, proxies=proxies, timeout=timeout)  # proxy 参数输入为 dict
        return response.status_code == 200
    except Exception as e:
        print(f"代理验证失败 {proxy}: {e}")
        return False

# 保存代理IP到数据库
def save_proxy_to_db(proxy: str, db_path: str = 'proxy_pool.db'):
    """保存代理IP到SQLite数据库
    
    Args:
        proxy: 代理IP地址，格式: 'http://proxy:port'
        db_path: 数据库路径
    """
    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 验证代理有效性
    is_valid = verify_proxy(proxy)
    use_flag = 1 if is_valid else 0
    
    try:
        # 检查是否已存在，如果存在则更新
        cursor.execute("SELECT * FROM proxyIp WHERE proxy = ?", (proxy,))
        result = cursor.fetchone()
        
        if result:
            cursor.execute(
                "UPDATE proxyIp SET use_flag = ?, update_datetime = ? WHERE proxy = ?",
                (use_flag, current_time, proxy)
            )
        else:
            cursor.execute(
                "INSERT INTO proxyIp (proxy, use_flag, create_datetime, update_datetime) VALUES (?, ?, ?, ?)",
                (proxy, use_flag, current_time, current_time)
            )
        
        conn.commit()
    except Exception as e:
        print(f"保存代理到数据库失败: {e}")
    finally:
        conn.close()

# 从数据库获取可用的代理IP
def get_valid_proxies(db_path: str = 'proxy_pool.db') -> List[str]:
    """从数据库中获取可用的代理IP
    
    Args:
        db_path: 数据库路径
        
    Returns:
        可用的代理IP列表 ['http://proxy:port', ...]
    """
    proxies = []
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT proxy FROM proxyIp WHERE use_flag = 1")
        rows = cursor.fetchall()
        
        for row in rows:
            proxies.append(row[0])
    except Exception as e:
        print(f"从数据库获取代理失败: {e}")
    finally:
        conn.close()
    
    return proxies

# 更新代理池
def update_proxy_pool(input_data: Union[str, List[str], Dict[str, str]], input_type: str = 'txt', db_path: str = 'proxy_pool.db'):
    """更新代理池
    
    Args:
        input_data: 输入数据，可以是文件路径、列表或字典
        input_type: 输入类型，可选值: 'txt', 'redis', 'python'
        db_path: 数据库路径
    """
    proxies = []
    
    # 确保数据库已初始化
    init_db(db_path)
    
    # 根据输入类型处理数据
    if input_type == 'txt':
        proxies = load_from_txt(input_data)
    elif input_type == 'redis':
        if isinstance(input_data, str):
            proxies = load_from_redis(input_data)
        else:
            proxies = load_from_redis()
    elif input_type == 'python':
        proxies = format_proxies(input_data)
    
    # 保存代理到数据库
    for proxy in proxies:
        save_proxy_to_db(proxy, db_path)
    
    return get_valid_proxies(db_path)

# 示例使用
if __name__ == "__main__":
    # 初始化数据库
    init_db()
    
    # 从txt文件加载代理
    # txt_proxies = load_from_txt("proxies.txt")
    # print(f"从txt加载的代理: {txt_proxies}")
    
    # 从Redis加载代理
    redis_proxies = load_from_redis()
    print(f"从Redis加载的代理: {redis_proxies}")
    for proxy in redis_proxies:
        save_proxy_to_db(proxy)
        # print(f"保存 redis 代理到数据库: {proxy}")

    
    # 使用Python列表格式
    list_proxies = ["http://127.0.0.1:8081", "http://127.0.0.1:8082"]
    print(f"列表格式的代理: {format_proxies(list_proxies)}")
    for proxy in list_proxies:
        save_proxy_to_db(proxy)
        # print(f"保存 list 代理到数据库: {proxy}")
    

    # 使用Python字典格式
    dict_proxies = {
        "http": "http://127.0.0.1:8083",
        "https": "http://127.0.0.1:8084"
    }
    print(f"字典格式的代理: {format_proxies(dict_proxies)}")

    for proxy in format_proxies(dict_proxies):
        save_proxy_to_db(proxy)
        # print(f"保存 dict 代理到数据库: {proxy}")
    
    valid_proxies = get_valid_proxies()
    print(f"可用的代理: {valid_proxies}")